#!/usr/bin/env python
# coding: utf-8

"""
VISUALIZATION AND EXPLORATORY DATA ANALYSIS (EDA) COMPONENTS
============================================================

This file contains all the visualization and exploratory data analysis components
extracted from the house price prediction project.

Components included:
1. Data Overview and Summary Statistics
2. Target Variable Analysis and Distribution
3. Correlation Analysis and Heatmaps
4. Categorical Feature Analysis
5. Numerical Feature Analysis
6. Outlier Detection and Visualization
7. Feature-Price Relationship Analysis
8. Neighborhood Analysis
9. Quality Features Impact Analysis
10. Seasonal and Time-based Analysis
11. Buyer Persona and Value Analysis

Dependencies: pandas, numpy, matplotlib, seaborn, scipy, sklearn, pickle
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
import pickle
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Lasso

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

class DataOverviewAnalyzer:
    """Class for basic data overview and summary statistics"""
    
    def __init__(self, df):
        self.df = df
        
    def display_basic_info(self):
        """Display basic dataset information"""
        print("Dataset shape:", self.df.shape)
        print("\nFirst 5 rows:")
        print(self.df.head())
        
        print("\nData types and non-null counts:")
        print(self.df.info())
        
        print("\nSummary statistics for numerical features:")
        print(self.df.describe())
        
    def analyze_missing_values(self):
        """Analyze missing values in the dataset"""
        missing_values = self.df.isnull().sum()
        missing_percent = (missing_values / len(self.df)) * 100
        missing_data = pd.concat([missing_values, missing_percent], axis=1)
        missing_data.columns = ['Missing Values', 'Percentage']
        print("\nMissing values analysis:")
        print(missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False))
        return missing_data

class TargetVariableAnalyzer:
    """Class for target variable analysis and visualization"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        os.makedirs(reports_dir, exist_ok=True)
        
    def analyze_target_distribution(self):
        """Analyze target variable distribution"""
        print(f"\n{self.target_col} statistics:")
        print(self.df[self.target_col].describe())
        
        # Plot distribution
        plt.figure(figsize=(10, 6))
        sns.histplot(self.df[self.target_col], kde=True)
        plt.title('Distribution of Sale Price')
        plt.savefig(f'{self.reports_dir}/saleprice_distribution.png')
        plt.close()
        
        # Check skewness
        skewness = self.df[self.target_col].skew()
        print(f"\nSkewness of {self.target_col}: {skewness}")
        
        return skewness
        
    def log_transform_analysis(self):
        """Analyze log-transformed target variable"""
        self.df['LogSalePrice'] = np.log1p(self.df[self.target_col])
        
        plt.figure(figsize=(10, 6))
        sns.histplot(self.df['LogSalePrice'], kde=True)
        plt.title('Distribution of Log-Transformed Sale Price')
        plt.savefig(f'{self.reports_dir}/log_saleprice_distribution.png')
        plt.close()
        
        return self.df['LogSalePrice']

class CorrelationAnalyzer:
    """Class for correlation analysis and visualization"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        
    def analyze_numerical_correlations(self):
        """Analyze correlations for numerical features"""
        numeric_features = self.df.select_dtypes(include=[np.number])
        correlation = numeric_features.corr()
        
        # Top features correlated with target
        target_corr = correlation[self.target_col].sort_values(ascending=False)
        print(f"\nTop 10 features correlated with {self.target_col}:")
        print(target_corr.head(11))
        
        return correlation, target_corr
        
    def plot_correlation_heatmap(self, correlation, top_n=11):
        """Plot correlation heatmap for top features"""
        top_corr_features = correlation[self.target_col].sort_values(ascending=False).head(top_n).index
        top_corr_matrix = correlation.loc[top_corr_features, top_corr_features]
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(top_corr_matrix, annot=True, cmap='coolwarm', linewidths=0.5)
        plt.title('Correlation Heatmap of Top Features')
        plt.savefig(f'{self.reports_dir}/correlation_heatmap.png')
        plt.close()

class CategoricalFeatureAnalyzer:
    """Class for categorical feature analysis"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        
    def analyze_categorical_features(self):
        """Analyze categorical features"""
        categorical_features = self.df.select_dtypes(include=['object'])
        print("\nCategorical features in the dataset:")
        print(categorical_features.columns.tolist())
        return categorical_features.columns.tolist()
        
    def plot_categorical_vs_target(self, features):
        """Plot categorical features vs target variable"""
        for feature in features:
            if feature in self.df.columns:
                plt.figure(figsize=(12, 6))
                sns.boxplot(x=feature, y=self.target_col, data=self.df)
                plt.title(f'{self.target_col} by {feature}')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/saleprice_by_{feature}.png')
                plt.close()

class NumericalFeatureAnalyzer:
    """Class for numerical feature analysis"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        
    def plot_numerical_vs_target(self, features):
        """Plot numerical features vs target variable"""
        for feature in features:
            if feature in self.df.columns:
                plt.figure(figsize=(10, 6))
                sns.scatterplot(x=feature, y=self.target_col, data=self.df)
                plt.title(f'{self.target_col} vs {feature}')
                plt.savefig(f'{self.reports_dir}/saleprice_vs_{feature}.png')
                plt.close()

class OutlierAnalyzer:
    """Class for outlier detection and visualization"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        
    def detect_outliers_visual(self, feature='GrLivArea'):
        """Visually detect outliers"""
        plt.figure(figsize=(10, 6))
        sns.scatterplot(x=feature, y=self.target_col, data=self.df)
        plt.title(f'{self.target_col} vs {feature} (Outlier Detection)')
        plt.savefig(f'{self.reports_dir}/outliers_{feature}.png')
        plt.close()

class FeaturePriceAnalyzer:
    """Class for detailed feature-price relationship analysis"""
    
    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        os.makedirs(f'{reports_dir}/feature_relationships', exist_ok=True)
        
    def analyze_numerical_relationships(self, features):
        """Analyze relationships between numerical features and price"""
        for feature in features:
            if feature in self.df.columns:
                # Create scatter plot with regression line
                plt.figure(figsize=(10, 6))
                sns.regplot(x=feature, y=self.target_col, data=self.df, 
                           scatter_kws={'alpha':0.5}, line_kws={'color':'red'})
                plt.title(f'Relationship between {feature} and Sale Price')
                plt.xlabel(feature)
                plt.ylabel('Sale Price ($)')
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_vs_price.png')
                plt.close()
                
                # Calculate correlation
                correlation = self.df[[feature, self.target_col]].corr().iloc[0, 1]
                print(f"\nFeature: {feature}")
                print(f"Correlation with Sale Price: {correlation:.4f}")
                
                # Bin analysis for non-categorical numerical features
                if feature not in ['OverallQual', 'OverallCond']:
                    self._analyze_binned_feature(feature)
                    
    def _analyze_binned_feature(self, feature, num_bins=5):
        """Analyze feature by creating bins and calculating average price"""
        try:
            self.df[f'{feature}_bin'] = pd.qcut(self.df[feature], num_bins, labels=False, duplicates='drop')
            price_by_bin = self.df.groupby(f'{feature}_bin')[self.target_col].agg(['mean', 'count']).reset_index()
            price_by_bin['mean'] = price_by_bin['mean'].round(2)
            print(f"Average price by {feature} bins:")
            print(price_by_bin)
            
            # Create bar plot
            plt.figure(figsize=(10, 6))
            sns.barplot(x=f'{feature}_bin', y='mean', data=price_by_bin)
            plt.title(f'Average Sale Price by {feature} Bins')
            plt.xlabel(f'{feature} (binned)')
            plt.ylabel('Average Sale Price ($)')
            plt.tight_layout()
            plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_bin_vs_price.png')
            plt.close()
        except Exception as e:
            print(f"Could not create bins for {feature}: {e}")
            
    def analyze_categorical_relationships(self, features):
        """Analyze relationships between categorical features and price"""
        for feature in features:
            if feature in self.df.columns:
                # Calculate average price by category
                price_by_category = self.df.groupby(feature)[self.target_col].agg(['mean', 'count']).sort_values('mean', ascending=False).reset_index()
                price_by_category['mean'] = price_by_category['mean'].round(2)
                
                print(f"\nFeature: {feature}")
                print(f"Average price by {feature} categories:")
                print(price_by_category)
                
                # Create bar plot
                plt.figure(figsize=(12, 6))
                sns.barplot(x=feature, y='mean', data=price_by_category)
                plt.title(f'Average Sale Price by {feature}')
                plt.xlabel(feature)
                plt.ylabel('Average Sale Price ($)')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_vs_price.png')
                plt.close()
