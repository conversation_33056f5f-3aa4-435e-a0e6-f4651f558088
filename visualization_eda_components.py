#!/usr/bin/env python
# coding: utf-8

"""
VISUALIZATION AND EXPLORATORY DATA ANALYSIS (EDA) COMPONENTS
============================================================

This file contains all the visualization and exploratory data analysis components
extracted from the house price prediction project.

Components included:
1. Data Overview and Summary Statistics
2. Target Variable Analysis and Distribution
3. Correlation Analysis and Heatmaps
4. Categorical Feature Analysis
5. Numerical Feature Analysis
6. Outlier Detection and Visualization
7. Feature-Price Relationship Analysis
8. Neighborhood Analysis
9. Quality Features Impact Analysis
10. Seasonal and Time-based Analysis
11. Buyer Persona and Value Analysis

Dependencies: pandas, numpy, matplotlib, seaborn, scipy, sklearn, pickle
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
import pickle
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Lasso

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

class DataOverviewAnalyzer:
    """Class for basic data overview and summary statistics"""

    def __init__(self, df):
        self.df = df

    def display_basic_info(self):
        """Display basic dataset information"""
        print("Dataset shape:", self.df.shape)
        print("\nFirst 5 rows:")
        print(self.df.head())

        print("\nData types and non-null counts:")
        print(self.df.info())

        print("\nSummary statistics for numerical features:")
        print(self.df.describe())

    def analyze_missing_values(self):
        """Analyze missing values in the dataset"""
        missing_values = self.df.isnull().sum()
        missing_percent = (missing_values / len(self.df)) * 100
        missing_data = pd.concat([missing_values, missing_percent], axis=1)
        missing_data.columns = ['Missing Values', 'Percentage']
        print("\nMissing values analysis:")
        print(missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False))
        return missing_data

class TargetVariableAnalyzer:
    """Class for target variable analysis and visualization"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        os.makedirs(reports_dir, exist_ok=True)

    def analyze_target_distribution(self):
        """Analyze target variable distribution"""
        print(f"\n{self.target_col} statistics:")
        print(self.df[self.target_col].describe())

        # Plot distribution
        plt.figure(figsize=(10, 6))
        sns.histplot(self.df[self.target_col], kde=True)
        plt.title('Distribution of Sale Price')
        plt.savefig(f'{self.reports_dir}/saleprice_distribution.png')
        plt.close()

        # Check skewness
        skewness = self.df[self.target_col].skew()
        print(f"\nSkewness of {self.target_col}: {skewness}")

        return skewness

    def log_transform_analysis(self):
        """Analyze log-transformed target variable"""
        self.df['LogSalePrice'] = np.log1p(self.df[self.target_col])

        plt.figure(figsize=(10, 6))
        sns.histplot(self.df['LogSalePrice'], kde=True)
        plt.title('Distribution of Log-Transformed Sale Price')
        plt.savefig(f'{self.reports_dir}/log_saleprice_distribution.png')
        plt.close()

        return self.df['LogSalePrice']

class CorrelationAnalyzer:
    """Class for correlation analysis and visualization"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_numerical_correlations(self):
        """Analyze correlations for numerical features"""
        numeric_features = self.df.select_dtypes(include=[np.number])
        correlation = numeric_features.corr()

        # Top features correlated with target
        target_corr = correlation[self.target_col].sort_values(ascending=False)
        print(f"\nTop 10 features correlated with {self.target_col}:")
        print(target_corr.head(11))

        return correlation, target_corr

    def plot_correlation_heatmap(self, correlation, top_n=11):
        """Plot correlation heatmap for top features"""
        top_corr_features = correlation[self.target_col].sort_values(ascending=False).head(top_n).index
        top_corr_matrix = correlation.loc[top_corr_features, top_corr_features]

        plt.figure(figsize=(12, 10))
        sns.heatmap(top_corr_matrix, annot=True, cmap='coolwarm', linewidths=0.5)
        plt.title('Correlation Heatmap of Top Features')
        plt.savefig(f'{self.reports_dir}/correlation_heatmap.png')
        plt.close()

class CategoricalFeatureAnalyzer:
    """Class for categorical feature analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_categorical_features(self):
        """Analyze categorical features"""
        categorical_features = self.df.select_dtypes(include=['object'])
        print("\nCategorical features in the dataset:")
        print(categorical_features.columns.tolist())
        return categorical_features.columns.tolist()

    def plot_categorical_vs_target(self, features):
        """Plot categorical features vs target variable"""
        for feature in features:
            if feature in self.df.columns:
                plt.figure(figsize=(12, 6))
                sns.boxplot(x=feature, y=self.target_col, data=self.df)
                plt.title(f'{self.target_col} by {feature}')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/saleprice_by_{feature}.png')
                plt.close()

class NumericalFeatureAnalyzer:
    """Class for numerical feature analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def plot_numerical_vs_target(self, features):
        """Plot numerical features vs target variable"""
        for feature in features:
            if feature in self.df.columns:
                plt.figure(figsize=(10, 6))
                sns.scatterplot(x=feature, y=self.target_col, data=self.df)
                plt.title(f'{self.target_col} vs {feature}')
                plt.savefig(f'{self.reports_dir}/saleprice_vs_{feature}.png')
                plt.close()

class OutlierAnalyzer:
    """Class for outlier detection and visualization"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def detect_outliers_visual(self, feature='GrLivArea'):
        """Visually detect outliers"""
        plt.figure(figsize=(10, 6))
        sns.scatterplot(x=feature, y=self.target_col, data=self.df)
        plt.title(f'{self.target_col} vs {feature} (Outlier Detection)')
        plt.savefig(f'{self.reports_dir}/outliers_{feature}.png')
        plt.close()

class FeaturePriceAnalyzer:
    """Class for detailed feature-price relationship analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        os.makedirs(f'{reports_dir}/feature_relationships', exist_ok=True)

    def analyze_numerical_relationships(self, features):
        """Analyze relationships between numerical features and price"""
        for feature in features:
            if feature in self.df.columns:
                # Create scatter plot with regression line
                plt.figure(figsize=(10, 6))
                sns.regplot(x=feature, y=self.target_col, data=self.df,
                           scatter_kws={'alpha':0.5}, line_kws={'color':'red'})
                plt.title(f'Relationship between {feature} and Sale Price')
                plt.xlabel(feature)
                plt.ylabel('Sale Price ($)')
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_vs_price.png')
                plt.close()

                # Calculate correlation
                correlation = self.df[[feature, self.target_col]].corr().iloc[0, 1]
                print(f"\nFeature: {feature}")
                print(f"Correlation with Sale Price: {correlation:.4f}")

                # Bin analysis for non-categorical numerical features
                if feature not in ['OverallQual', 'OverallCond']:
                    self._analyze_binned_feature(feature)

    def _analyze_binned_feature(self, feature, num_bins=5):
        """Analyze feature by creating bins and calculating average price"""
        try:
            self.df[f'{feature}_bin'] = pd.qcut(self.df[feature], num_bins, labels=False, duplicates='drop')
            price_by_bin = self.df.groupby(f'{feature}_bin')[self.target_col].agg(['mean', 'count']).reset_index()
            price_by_bin['mean'] = price_by_bin['mean'].round(2)
            print(f"Average price by {feature} bins:")
            print(price_by_bin)

            # Create bar plot
            plt.figure(figsize=(10, 6))
            sns.barplot(x=f'{feature}_bin', y='mean', data=price_by_bin)
            plt.title(f'Average Sale Price by {feature} Bins')
            plt.xlabel(f'{feature} (binned)')
            plt.ylabel('Average Sale Price ($)')
            plt.tight_layout()
            plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_bin_vs_price.png')
            plt.close()
        except Exception as e:
            print(f"Could not create bins for {feature}: {e}")

    def analyze_categorical_relationships(self, features):
        """Analyze relationships between categorical features and price"""
        for feature in features:
            if feature in self.df.columns:
                # Calculate average price by category
                price_by_category = self.df.groupby(feature)[self.target_col].agg(['mean', 'count']).sort_values('mean', ascending=False).reset_index()
                price_by_category['mean'] = price_by_category['mean'].round(2)

                print(f"\nFeature: {feature}")
                print(f"Average price by {feature} categories:")
                print(price_by_category)

                # Create bar plot
                plt.figure(figsize=(12, 6))
                sns.barplot(x=feature, y='mean', data=price_by_category)
                plt.title(f'Average Sale Price by {feature}')
                plt.xlabel(feature)
                plt.ylabel('Average Sale Price ($)')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(f'{self.reports_dir}/feature_relationships/{feature}_vs_price.png')
                plt.close()

class PriceSensitivityAnalyzer:
    """Class for price sensitivity analysis"""

    def __init__(self, df, model, scaler, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.model = model
        self.scaler = scaler
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_price_sensitivity(self, features):
        """Analyze price sensitivity to feature changes"""
        # Create reference house (median values)
        reference_house = pd.DataFrame(columns=self.df.drop([self.target_col, 'LogSalePrice', 'PricePerSF'], axis=1).columns)
        for col in reference_house.columns:
            if self.df[col].dtype in ['int64', 'float64']:
                reference_house.loc[0, col] = self.df[col].median()
            else:
                reference_house.loc[0, col] = self.df[col].mode()[0]

        # Scale reference house
        reference_house_scaled = self.scaler.transform(reference_house)
        reference_price_log = self.model.predict(reference_house_scaled)[0]
        reference_price = np.expm1(reference_price_log)

        print(f"\nReference house estimated price: ${reference_price:.2f}")

        sensitivity_results = []

        for feature in features:
            if feature in reference_house.columns:
                current_value = reference_house.loc[0, feature]

                # 10% increase
                modified_house_plus = reference_house.copy()
                modified_house_plus.loc[0, feature] = current_value * 1.1
                modified_house_plus_scaled = self.scaler.transform(modified_house_plus)
                price_plus = np.expm1(self.model.predict(modified_house_plus_scaled)[0])

                # 10% decrease
                modified_house_minus = reference_house.copy()
                modified_house_minus.loc[0, feature] = current_value * 0.9
                modified_house_minus_scaled = self.scaler.transform(modified_house_minus)
                price_minus = np.expm1(self.model.predict(modified_house_minus_scaled)[0])

                # Calculate price changes
                pct_change_plus = (price_plus - reference_price) / reference_price * 100
                pct_change_minus = (price_minus - reference_price) / reference_price * 100

                sensitivity_results.append({
                    'Feature': feature,
                    'Current Value': current_value,
                    'Price with 10% Increase': price_plus,
                    'Price with 10% Decrease': price_minus,
                    'Price Change % (Increase)': pct_change_plus,
                    'Price Change % (Decrease)': pct_change_minus,
                    'Price Elasticity': (pct_change_plus - pct_change_minus) / 20
                })

        sensitivity_df = pd.DataFrame(sensitivity_results)
        sensitivity_df = sensitivity_df.sort_values('Price Elasticity', ascending=False)

        # Plot price elasticity
        plt.figure(figsize=(12, 6))
        sns.barplot(x='Feature', y='Price Elasticity', data=sensitivity_df)
        plt.title('Price Elasticity by Feature')
        plt.xlabel('Feature')
        plt.ylabel('Price Elasticity (% Price Change per % Feature Change)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/price_elasticity.png')
        plt.close()

        return sensitivity_df

class NeighborhoodAnalyzer:
    """Class for neighborhood analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_neighborhood_prices(self):
        """Analyze price variations by neighborhood"""
        neighborhood_price = self.df.groupby('Neighborhood')[self.target_col].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
        neighborhood_price['mean'] = neighborhood_price['mean'].round(2)
        neighborhood_price['median'] = neighborhood_price['median'].round(2)

        print("\nAverage and median price by neighborhood:")
        print(neighborhood_price)

        # Plot average price by neighborhood
        plt.figure(figsize=(14, 8))
        sns.barplot(x='Neighborhood', y='mean', data=neighborhood_price)
        plt.title('Average Sale Price by Neighborhood')
        plt.xlabel('Neighborhood')
        plt.ylabel('Average Sale Price ($)')
        plt.xticks(rotation=90)
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/neighborhood_vs_price.png')
        plt.close()

        return neighborhood_price

    def analyze_price_per_sqft_by_neighborhood(self):
        """Analyze price per square foot by neighborhood"""
        self.df['PricePerSqFt'] = self.df[self.target_col] / self.df['GrLivArea']
        price_per_sqft = self.df.groupby('Neighborhood')['PricePerSqFt'].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
        price_per_sqft['mean'] = price_per_sqft['mean'].round(2)
        price_per_sqft['median'] = price_per_sqft['median'].round(2)

        print("\nAverage and median price per square foot by neighborhood:")
        print(price_per_sqft)

        # Plot price per square foot by neighborhood
        plt.figure(figsize=(14, 8))
        sns.barplot(x='Neighborhood', y='mean', data=price_per_sqft)
        plt.title('Average Price per Square Foot by Neighborhood')
        plt.xlabel('Neighborhood')
        plt.ylabel('Average Price per Square Foot ($)')
        plt.xticks(rotation=90)
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/neighborhood_price_per_sqft.png')
        plt.close()

        return price_per_sqft

class QualityAnalyzer:
    """Class for quality features analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_overall_quality_impact(self):
        """Analyze overall quality impact on price"""
        quality_price = self.df.groupby('OverallQual')[self.target_col].agg(['mean', 'median', 'count']).reset_index()
        quality_price['mean'] = quality_price['mean'].round(2)
        quality_price['median'] = quality_price['median'].round(2)

        print("\nAverage and median price by overall quality:")
        print(quality_price)

        # Plot average price by overall quality
        plt.figure(figsize=(12, 6))
        sns.barplot(x='OverallQual', y='mean', data=quality_price)
        plt.title('Average Sale Price by Overall Quality')
        plt.xlabel('Overall Quality (1-10 scale)')
        plt.ylabel('Average Sale Price ($)')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/overall_quality_vs_price.png')
        plt.close()

        return quality_price

    def analyze_kitchen_quality_impact(self):
        """Analyze kitchen quality impact on price"""
        if 'KitchenQual' in self.df.columns:
            kitchen_price = self.df.groupby('KitchenQual')[self.target_col].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
            kitchen_price['mean'] = kitchen_price['mean'].round(2)
            kitchen_price['median'] = kitchen_price['median'].round(2)

            print("\nAverage and median price by kitchen quality:")
            print(kitchen_price)

            # Plot average price by kitchen quality
            plt.figure(figsize=(10, 6))
            sns.barplot(x='KitchenQual', y='mean', data=kitchen_price)
            plt.title('Average Sale Price by Kitchen Quality')
            plt.xlabel('Kitchen Quality')
            plt.ylabel('Average Sale Price ($)')
            plt.tight_layout()
            plt.savefig(f'{self.reports_dir}/kitchen_quality_vs_price.png')
            plt.close()

            return kitchen_price

class AgeAnalyzer:
    """Class for age-related analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_house_age_impact(self):
        """Analyze house age impact on price"""
        # Create age bins
        self.df['HouseAgeBin'] = pd.cut(self.df['HouseAge'], bins=[0, 10, 20, 30, 40, 50, 100],
                                       labels=['0-10', '11-20', '21-30', '31-40', '41-50', '51+'])

        age_price = self.df.groupby('HouseAgeBin')[self.target_col].agg(['mean', 'median', 'count']).reset_index()
        age_price['mean'] = age_price['mean'].round(2)
        age_price['median'] = age_price['median'].round(2)

        print("\nAverage and median price by house age:")
        print(age_price)

        # Plot average price by house age
        plt.figure(figsize=(12, 6))
        sns.barplot(x='HouseAgeBin', y='mean', data=age_price)
        plt.title('Average Sale Price by House Age')
        plt.xlabel('House Age (years)')
        plt.ylabel('Average Sale Price ($)')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/house_age_vs_price.png')
        plt.close()

        return age_price

    def analyze_year_built_trend(self):
        """Analyze year built vs price trend"""
        plt.figure(figsize=(12, 6))
        sns.regplot(x='YearBuilt', y=self.target_col, data=self.df,
                   scatter_kws={'alpha':0.5}, line_kws={'color':'red'})
        plt.title('Relationship between Year Built and Sale Price')
        plt.xlabel('Year Built')
        plt.ylabel('Sale Price ($)')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/year_built_vs_price.png')
        plt.close()

class SizeAnalyzer:
    """Class for size-related analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_house_size_impact(self):
        """Analyze house size impact on price"""
        # Create size bins
        self.df['TotalSFBin'] = pd.qcut(self.df['TotalSF'], 6,
                                       labels=['Very Small', 'Small', 'Medium-Small',
                                              'Medium-Large', 'Large', 'Very Large'])

        size_price = self.df.groupby('TotalSFBin')[self.target_col].agg(['mean', 'median', 'count']).reset_index()
        size_price['mean'] = size_price['mean'].round(2)
        size_price['median'] = size_price['median'].round(2)

        print("\nAverage and median price by house size:")
        print(size_price)

        # Plot average price by house size
        plt.figure(figsize=(12, 6))
        sns.barplot(x='TotalSFBin', y='mean', data=size_price)
        plt.title('Average Sale Price by House Size')
        plt.xlabel('House Size')
        plt.ylabel('Average Sale Price ($)')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/house_size_vs_price.png')
        plt.close()

        return size_price

class SeasonalAnalyzer:
    """Class for seasonal and time-based analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir

    def analyze_monthly_price_trends(self):
        """Analyze price trends by month sold"""
        monthly_price = self.df.groupby('MoSold')[self.target_col].agg(['mean', 'median', 'count']).reset_index()
        monthly_price.columns = ['Month', 'AvgPrice', 'MedianPrice', 'Count']
        monthly_price['AvgPrice'] = monthly_price['AvgPrice'].round(2)
        monthly_price['MedianPrice'] = monthly_price['MedianPrice'].round(2)

        # Sort by month
        monthly_price = monthly_price.sort_values('Month')

        # Add month names
        month_names = {
            1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
            7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
        }
        monthly_price['MonthName'] = monthly_price['Month'].map(month_names)

        print("\nAverage price by month sold:")
        print(monthly_price)

        # Plot average price by month
        plt.figure(figsize=(12, 6))
        sns.barplot(x='MonthName', y='AvgPrice', data=monthly_price)
        plt.title('Average House Price by Month Sold')
        plt.xlabel('Month')
        plt.ylabel('Average Price ($)')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/price_by_month.png')
        plt.close()

        return monthly_price

class BuyerPersonaAnalyzer:
    """Class for buyer persona and value analysis"""

    def __init__(self, df, target_col='SalePrice', reports_dir='../reports'):
        self.df = df
        self.target_col = target_col
        self.reports_dir = reports_dir
        os.makedirs(f'{reports_dir}/buyer_suggestions', exist_ok=True)

    def analyze_value_by_neighborhood(self):
        """Analyze value score by neighborhood"""
        # Calculate price per square foot
        self.df['PricePerSqFt'] = self.df[self.target_col] / self.df['GrLivArea']

        # Calculate metrics by neighborhood
        neighborhood_metrics = self.df.groupby('Neighborhood').agg({
            self.target_col: ['mean', 'median', 'min', 'max', 'count'],
            'PricePerSqFt': ['mean', 'median'],
            'OverallQual': 'mean',
            'GrLivArea': 'mean',
            'YearBuilt': 'mean'
        }).reset_index()

        # Flatten column names
        neighborhood_metrics.columns = ['_'.join(col).strip('_') for col in neighborhood_metrics.columns.values]
        neighborhood_metrics = neighborhood_metrics.rename(columns={
            'Neighborhood_': 'Neighborhood',
            f'{self.target_col}_mean': 'AvgPrice',
            f'{self.target_col}_median': 'MedianPrice',
            f'{self.target_col}_min': 'MinPrice',
            f'{self.target_col}_max': 'MaxPrice',
            f'{self.target_col}_count': 'Count',
            'PricePerSqFt_mean': 'AvgPricePerSqFt',
            'PricePerSqFt_median': 'MedianPricePerSqFt',
            'OverallQual_mean': 'AvgQuality',
            'GrLivArea_mean': 'AvgSize',
            'YearBuilt_mean': 'AvgYearBuilt'
        })

        # Calculate value score (higher is better value)
        neighborhood_metrics['ValueScore'] = (neighborhood_metrics['AvgQuality'] / neighborhood_metrics['AvgPricePerSqFt']) * 100
        neighborhood_metrics['ValueScore'] = neighborhood_metrics['ValueScore'].round(2)

        # Sort by value score
        neighborhood_metrics_by_value = neighborhood_metrics.sort_values('ValueScore', ascending=False)

        print("\nNeighborhoods ranked by value score (higher is better value):")
        print(neighborhood_metrics_by_value[['Neighborhood', 'ValueScore', 'AvgPrice', 'AvgPricePerSqFt', 'AvgQuality', 'AvgSize', 'AvgYearBuilt', 'Count']].head(10))

        # Plot value score by neighborhood
        plt.figure(figsize=(14, 8))
        sns.barplot(x='Neighborhood', y='ValueScore', data=neighborhood_metrics_by_value.head(15))
        plt.title('Top 15 Neighborhoods by Value Score')
        plt.xlabel('Neighborhood')
        plt.ylabel('Value Score (Quality/Price per SqFt * 100)')
        plt.xticks(rotation=90)
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/buyer_suggestions/neighborhood_value_score.png')
        plt.close()

        return neighborhood_metrics_by_value

    def analyze_budget_recommendations(self):
        """Generate budget-based recommendations"""
        budget_ranges = [
            (0, 150000, 'Low Budget (Under $150K)'),
            (150000, 250000, 'Medium Budget ($150K-$250K)'),
            (250000, 350000, 'High Budget ($250K-$350K)'),
            (350000, float('inf'), 'Luxury Budget (Over $350K)')
        ]

        budget_recommendations = []

        for min_price, max_price, budget_label in budget_ranges:
            # Filter houses in this budget range
            budget_df = self.df[(self.df[self.target_col] >= min_price) & (self.df[self.target_col] < max_price)]

            if len(budget_df) == 0:
                continue

            # Find top neighborhoods by count
            top_neighborhoods = budget_df['Neighborhood'].value_counts().head(5).index.tolist()

            # Calculate average metrics for this budget range
            avg_metrics = {
                'Budget': budget_label,
                'PriceRange': f"${min_price:,.0f} - ${max_price:,.0f}",
                'Count': len(budget_df),
                'AvgSize': budget_df['GrLivArea'].mean(),
                'AvgQuality': budget_df['OverallQual'].mean(),
                'AvgAge': 2010 - budget_df['YearBuilt'].mean(),
                'AvgBaths': budget_df['FullBath'].mean() + (0.5 * budget_df['HalfBath'].mean()),
                'AvgBedrooms': budget_df['BedroomAbvGr'].mean(),
                'TopNeighborhoods': ', '.join(top_neighborhoods)
            }

            budget_recommendations.append(avg_metrics)

        budget_recommendations_df = pd.DataFrame(budget_recommendations)
        print("\nRecommendations by budget range:")
        print(budget_recommendations_df)

        return budget_recommendations_df

    def analyze_size_quality_tradeoffs(self):
        """Analyze size vs quality vs location trade-offs"""
        # Create size and quality categories
        self.df['SizeCategory'] = pd.qcut(self.df['GrLivArea'], 3, labels=['Small', 'Medium', 'Large'])
        self.df['QualityCategory'] = pd.cut(self.df['OverallQual'], bins=[0, 4, 7, 10], labels=['Basic', 'Good', 'Excellent'])

        # Calculate average price for each combination
        size_quality_price = self.df.groupby(['SizeCategory', 'QualityCategory'])[self.target_col].agg(['mean', 'count']).reset_index()
        size_quality_price.columns = ['Size', 'Quality', 'AvgPrice', 'Count']
        size_quality_price['AvgPrice'] = size_quality_price['AvgPrice'].round(2)

        print("\nAverage price by size and quality combination:")
        print(size_quality_price)

        # Create heatmap
        plt.figure(figsize=(10, 6))
        pivot_table = size_quality_price.pivot(index='Size', columns='Quality', values='AvgPrice')
        sns.heatmap(pivot_table, annot=True, fmt=',.0f', cmap='YlGnBu')
        plt.title('Average House Price by Size and Quality')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/buyer_suggestions/size_quality_price_heatmap.png')
        plt.close()

        return size_quality_price

# Example usage function
def run_comprehensive_eda(df, target_col='SalePrice', reports_dir='reports'):
    """
    Run comprehensive EDA using all analyzer classes

    Args:
        df: DataFrame with house price data
        target_col: Name of target variable column
        reports_dir: Directory to save reports and visualizations

    Returns:
        Dictionary containing results from all analyzers
    """
    results = {}

    # Basic data overview
    overview = DataOverviewAnalyzer(df)
    overview.display_basic_info()
    results['missing_data'] = overview.analyze_missing_values()

    # Target variable analysis
    target_analyzer = TargetVariableAnalyzer(df, target_col, reports_dir)
    results['target_skewness'] = target_analyzer.analyze_target_distribution()
    results['log_target'] = target_analyzer.log_transform_analysis()

    # Correlation analysis
    corr_analyzer = CorrelationAnalyzer(df, target_col, reports_dir)
    correlation, target_corr = corr_analyzer.analyze_numerical_correlations()
    corr_analyzer.plot_correlation_heatmap(correlation)
    results['correlation'] = correlation
    results['target_correlation'] = target_corr

    # Categorical features
    cat_analyzer = CategoricalFeatureAnalyzer(df, target_col, reports_dir)
    cat_features = cat_analyzer.analyze_categorical_features()
    important_cat_features = ['Neighborhood', 'ExterQual', 'KitchenQual', 'BsmtQual', 'GarageType']
    cat_analyzer.plot_categorical_vs_target([f for f in important_cat_features if f in cat_features])

    # Numerical features
    num_analyzer = NumericalFeatureAnalyzer(df, target_col, reports_dir)
    important_num_features = ['OverallQual', 'GrLivArea', 'GarageCars', 'TotalBsmtSF', 'YearBuilt']
    num_analyzer.plot_numerical_vs_target([f for f in important_num_features if f in df.columns])

    # Outlier detection
    outlier_analyzer = OutlierAnalyzer(df, target_col, reports_dir)
    outlier_analyzer.detect_outliers_visual('GrLivArea')

    print("\nComprehensive EDA completed. Check the reports directory for visualizations.")
    return results