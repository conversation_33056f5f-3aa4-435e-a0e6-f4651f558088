#!/usr/bin/env python
# coding: utf-8

# # House Price Prediction - Feature-Price Relationship Analysis
# 
# This notebook analyzes the relationships between house features and price based on model results and feature importance.

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import os
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Lasso
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

# Load the engineered data
df = pd.read_csv('../data/engineered_data.csv')
print(f"Dataset shape: {df.shape}")

# Load the best model (Lasso Regression based on previous results)
with open('../models/lasso_regression.pkl', 'rb') as f:
    lasso_model = pickle.load(f)

# Load feature importance/coefficients
feature_coef = pd.read_csv('../reports/feature_coefficients.csv')
print("\nTop 10 features with highest coefficients:")
print(feature_coef.head(10))

# -----------------------------------------------------------------------------
# 1. Analyze Key Feature-Price Relationships
# -----------------------------------------------------------------------------
print("\n--- Analyzing Key Feature-Price Relationships ---")

# Define key features based on model coefficients and domain knowledge
key_features = [
    'GrLivArea', 'TotalSF', 'OverallQual', 'OverallCond', 'HouseAge', 
    'BsmtFinSF1', 'Functional', 'YearBuilt', 'TotalBath', 'GarageCars',
    'Neighborhood', 'KitchenQual', 'ExterQual', 'BsmtQual'
]

# Create a directory for feature relationship plots
os.makedirs('../reports/feature_relationships', exist_ok=True)

# Analyze numerical features
numerical_features = [
    'GrLivArea', 'TotalSF', 'OverallQual', 'OverallCond', 'HouseAge', 
    'BsmtFinSF1', 'YearBuilt', 'TotalBath', 'GarageCars'
]

for feature in numerical_features:
    if feature in df.columns:
        # Create scatter plot
        plt.figure(figsize=(10, 6))
        sns.regplot(x=feature, y='SalePrice', data=df, scatter_kws={'alpha':0.5}, line_kws={'color':'red'})
        plt.title(f'Relationship between {feature} and Sale Price')
        plt.xlabel(feature)
        plt.ylabel('Sale Price ($)')
        plt.tight_layout()
        plt.savefig(f'../reports/feature_relationships/{feature}_vs_price.png')
        plt.close()
        
        # Calculate correlation
        correlation = df[[feature, 'SalePrice']].corr().iloc[0, 1]
        
        # Print analysis
        print(f"\nFeature: {feature}")
        print(f"Correlation with Sale Price: {correlation:.4f}")
        
        # Bin the feature and analyze average price by bin
        if feature != 'OverallQual' and feature != 'OverallCond':  # These are already categorical
            num_bins = 5
            df[f'{feature}_bin'] = pd.qcut(df[feature], num_bins, labels=False, duplicates='drop')
            price_by_bin = df.groupby(f'{feature}_bin')['SalePrice'].agg(['mean', 'count']).reset_index()
            price_by_bin['mean'] = price_by_bin['mean'].round(2)
            print(f"Average price by {feature} bins:")
            print(price_by_bin)
            
            # Create bar plot of average price by bin
            plt.figure(figsize=(10, 6))
            sns.barplot(x=f'{feature}_bin', y='mean', data=price_by_bin)
            plt.title(f'Average Sale Price by {feature} Bins')
            plt.xlabel(f'{feature} (binned)')
            plt.ylabel('Average Sale Price ($)')
            plt.tight_layout()
            plt.savefig(f'../reports/feature_relationships/{feature}_bin_vs_price.png')
            plt.close()

# Analyze categorical features
categorical_features = ['Neighborhood', 'KitchenQual', 'ExterQual', 'BsmtQual']

for feature in categorical_features:
    if feature in df.columns:
        # Calculate average price by category
        price_by_category = df.groupby(feature)['SalePrice'].agg(['mean', 'count']).sort_values('mean', ascending=False).reset_index()
        price_by_category['mean'] = price_by_category['mean'].round(2)
        
        print(f"\nFeature: {feature}")
        print(f"Average price by {feature} categories:")
        print(price_by_category)
        
        # Create bar plot of average price by category
        plt.figure(figsize=(12, 6))
        sns.barplot(x=feature, y='mean', data=price_by_category)
        plt.title(f'Average Sale Price by {feature}')
        plt.xlabel(feature)
        plt.ylabel('Average Sale Price ($)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'../reports/feature_relationships/{feature}_vs_price.png')
        plt.close()

# -----------------------------------------------------------------------------
# 2. Analyze Price Sensitivity to Feature Changes
# -----------------------------------------------------------------------------
print("\n--- Analyzing Price Sensitivity to Feature Changes ---")

# Create a reference house (median values for numerical features, mode for categorical)
reference_house = pd.DataFrame(columns=df.drop(['SalePrice', 'LogSalePrice', 'PricePerSF'], axis=1).columns)
for col in reference_house.columns:
    if df[col].dtype in ['int64', 'float64']:
        reference_house.loc[0, col] = df[col].median()
    else:
        reference_house.loc[0, col] = df[col].mode()[0]

# Scale the reference house (same as in model training)
X = df.drop(['SalePrice', 'LogSalePrice', 'PricePerSF'], axis=1)
scaler = StandardScaler()
scaler.fit(X)
reference_house_scaled = scaler.transform(reference_house)

# Predict the price of the reference house
reference_price_log = lasso_model.predict(reference_house_scaled)[0]
reference_price = np.expm1(reference_price_log)
print(f"\nReference house estimated price: ${reference_price:.2f}")

# Analyze price sensitivity for key numerical features
sensitivity_results = []

for feature in numerical_features:
    if feature in reference_house.columns:
        # Get the current value
        current_value = reference_house.loc[0, feature]
        
        # Calculate price with 10% increase
        modified_house_plus = reference_house.copy()
        modified_house_plus.loc[0, feature] = current_value * 1.1
        modified_house_plus_scaled = scaler.transform(modified_house_plus)
        price_plus_log = lasso_model.predict(modified_house_plus_scaled)[0]
        price_plus = np.expm1(price_plus_log)
        
        # Calculate price with 10% decrease
        modified_house_minus = reference_house.copy()
        modified_house_minus.loc[0, feature] = current_value * 0.9
        modified_house_minus_scaled = scaler.transform(modified_house_minus)
        price_minus_log = lasso_model.predict(modified_house_minus_scaled)[0]
        price_minus = np.expm1(price_minus_log)
        
        # Calculate price change percentages
        pct_change_plus = (price_plus - reference_price) / reference_price * 100
        pct_change_minus = (price_minus - reference_price) / reference_price * 100
        
        # Store results
        sensitivity_results.append({
            'Feature': feature,
            'Current Value': current_value,
            'Price with 10% Increase': price_plus,
            'Price with 10% Decrease': price_minus,
            'Price Change % (Increase)': pct_change_plus,
            'Price Change % (Decrease)': pct_change_minus,
            'Price Elasticity': (pct_change_plus - pct_change_minus) / 20  # Average % price change per % feature change
        })

# Convert to DataFrame and sort by price elasticity
sensitivity_df = pd.DataFrame(sensitivity_results)
sensitivity_df = sensitivity_df.sort_values('Price Elasticity', ascending=False)
print("\nPrice sensitivity analysis for key features:")
print(sensitivity_df)

# Save sensitivity analysis to CSV
sensitivity_df.to_csv('../reports/price_sensitivity.csv', index=False)

# Create bar plot of price elasticity
plt.figure(figsize=(12, 6))
sns.barplot(x='Feature', y='Price Elasticity', data=sensitivity_df)
plt.title('Price Elasticity by Feature')
plt.xlabel('Feature')
plt.ylabel('Price Elasticity (% Price Change per % Feature Change)')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('../reports/price_elasticity.png')
plt.close()

# -----------------------------------------------------------------------------
# 3. Analyze Neighborhood Impact on Price
# -----------------------------------------------------------------------------
print("\n--- Analyzing Neighborhood Impact on Price ---")

# Calculate average price by neighborhood
neighborhood_price = df.groupby('Neighborhood')['SalePrice'].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
neighborhood_price['mean'] = neighborhood_price['mean'].round(2)
neighborhood_price['median'] = neighborhood_price['median'].round(2)

print("\nAverage and median price by neighborhood:")
print(neighborhood_price)

# Create bar plot of average price by neighborhood
plt.figure(figsize=(14, 8))
sns.barplot(x='Neighborhood', y='mean', data=neighborhood_price)
plt.title('Average Sale Price by Neighborhood')
plt.xlabel('Neighborhood')
plt.ylabel('Average Sale Price ($)')
plt.xticks(rotation=90)
plt.tight_layout()
plt.savefig('../reports/neighborhood_vs_price.png')
plt.close()

# Analyze price per square foot by neighborhood
df['PricePerSqFt'] = df['SalePrice'] / df['GrLivArea']
price_per_sqft = df.groupby('Neighborhood')['PricePerSqFt'].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
price_per_sqft['mean'] = price_per_sqft['mean'].round(2)
price_per_sqft['median'] = price_per_sqft['median'].round(2)

print("\nAverage and median price per square foot by neighborhood:")
print(price_per_sqft)

# Create bar plot of price per square foot by neighborhood
plt.figure(figsize=(14, 8))
sns.barplot(x='Neighborhood', y='mean', data=price_per_sqft)
plt.title('Average Price per Square Foot by Neighborhood')
plt.xlabel('Neighborhood')
plt.ylabel('Average Price per Square Foot ($)')
plt.xticks(rotation=90)
plt.tight_layout()
plt.savefig('../reports/neighborhood_price_per_sqft.png')
plt.close()

# -----------------------------------------------------------------------------
# 4. Analyze Quality Features Impact on Price
# -----------------------------------------------------------------------------
print("\n--- Analyzing Quality Features Impact on Price ---")

# Analyze overall quality impact
quality_price = df.groupby('OverallQual')['SalePrice'].agg(['mean', 'median', 'count']).reset_index()
quality_price['mean'] = quality_price['mean'].round(2)
quality_price['median'] = quality_price['median'].round(2)

print("\nAverage and median price by overall quality:")
print(quality_price)

# Create bar plot of average price by overall quality
plt.figure(figsize=(12, 6))
sns.barplot(x='OverallQual', y='mean', data=quality_price)
plt.title('Average Sale Price by Overall Quality')
plt.xlabel('Overall Quality (1-10 scale)')
plt.ylabel('Average Sale Price ($)')
plt.tight_layout()
plt.savefig('../reports/overall_quality_vs_price.png')
plt.close()

# Analyze kitchen quality impact
if 'KitchenQual' in df.columns:
    kitchen_price = df.groupby('KitchenQual')['SalePrice'].agg(['mean', 'median', 'count']).sort_values('mean', ascending=False).reset_index()
    kitchen_price['mean'] = kitchen_price['mean'].round(2)
    kitchen_price['median'] = kitchen_price['median'].round(2)
    
    print("\nAverage and median price by kitchen quality:")
    print(kitchen_price)
    
    # Create bar plot of average price by kitchen quality
    plt.figure(figsize=(10, 6))
    sns.barplot(x='KitchenQual', y='mean', data=kitchen_price)
    plt.title('Average Sale Price by Kitchen Quality')
    plt.xlabel('Kitchen Quality')
    plt.ylabel('Average Sale Price ($)')
    plt.tight_layout()
    plt.savefig('../reports/kitchen_quality_vs_price.png')
    plt.close()

# -----------------------------------------------------------------------------
# 5. Analyze Age-Related Features Impact on Price
# -----------------------------------------------------------------------------
print("\n--- Analyzing Age-Related Features Impact on Price ---")

# Create age bins
df['HouseAgeBin'] = pd.cut(df['HouseAge'], bins=[0, 10, 20, 30, 40, 50, 100], labels=['0-10', '11-20', '21-30', '31-40', '41-50', '51+'])

# Calculate average price by house age bin
age_price = df.groupby('HouseAgeBin')['SalePrice'].agg(['mean', 'median', 'count']).reset_index()
age_price['mean'] = age_price['mean'].round(2)
age_price['median'] = age_price['median'].round(2)

print("\nAverage and median price by house age:")
print(age_price)

# Create bar plot of average price by house age
plt.figure(figsize=(12, 6))
sns.barplot(x='HouseAgeBin', y='mean', data=age_price)
plt.title('Average Sale Price by House Age')
plt.xlabel('House Age (years)')
plt.ylabel('Average Sale Price ($)')
plt.tight_layout()
plt.savefig('../reports/house_age_vs_price.png')
plt.close()

# Analyze year built vs price
plt.figure(figsize=(12, 6))
sns.regplot(x='YearBuilt', y='SalePrice', data=df, scatter_kws={'alpha':0.5}, line_kws={'color':'red'})
plt.title('Relationship between Year Built and Sale Price')
plt.xlabel('Year Built')
plt.ylabel('Sale Price ($)')
plt.tight_layout()
plt.savefig('../reports/year_built_vs_price.png')
plt.close()

# -----------------------------------------------------------------------------
# 6. Analyze Size-Related Features Impact on Price
# -----------------------------------------------------------------------------
print("\n--- Analyzing Size-Related Features Impact on Price ---")

# Create total square footage bins
df['TotalSFBin'] = pd.qcut(df['TotalSF'], 6, labels=['Very Small', 'Small', 'Medium-Small', 'Medium-Large', 'Large', 'Very Large'])

# Calculate average price by total square footage bin
size_price = df.groupby('TotalSFBin')['SalePrice'].agg(['mean', 'median', 'count']).reset_index()
size_price['mean'] = size_price['mean'].round(2)
size_price['median'] = size_price['median'].round(2)

print("\nAverage and median price by house size:")
print(size_price)

# Create bar plot of average price by house size
plt.figure(figsize=(12, 6))
sns.barplot(x='TotalSFBin', y='mean', data=size_price)
plt.title('Average Sale Price by House Size')
plt.xlabel('House Size')
plt.ylabel('Average Sale Price ($)')
plt.tight_layout()
plt.savefig('../reports/house_size_vs_price.png')
plt.close()

# Analyze price per square foot by house size
df['PricePerSqFt'] = df['SalePrice'] / df['GrLivArea']
price_per_sqft_by_size = df.groupby('TotalSFBin')['PricePerSqFt'].agg(['mean', 'median']).reset_index()
price_per_sqft_by_size['mean'] = price_per_sqft_by_size['mean'].round(2)
price_per_sqft_by_size['median'] = price_per_sqft_by_size['median'].round(2)

print("\nAverage and median price per square foot by house size:")
print(price_per_sqft_by_size)

# Create bar plot of price per square foot by house size
plt.figure(figsize=(12, 6))
sns.barplot(x='TotalSFBin', y='mean', data=price_per_sqft_by_size)
plt.title('Average Price per Square Foot by House Size')
plt.xlabel('House Size')
plt.ylabel('Average Price per Square Foot ($)')
plt.tight_layout()
plt.savefig('../reports/house_size_price_per_sqft.png')
plt.close()

# -----------------------------------------------------------------------------
# 7. Analyze Garage and Basement Features Impact on Price
# -----------------------------------------------------------------------------
print("\n--- Analyzing Garage and Basement Features Impact on Price ---")

# Analyze garage cars impact
garage_price = df.groupby('GarageCars')['SalePrice'].agg(['mean', 'median', 'count']).reset_index()
garage_price['mean'] = garage_price['mean'].round(2)
garage_price['median'] = garage_price['median'].round(2)

print("\nAverage and median price by garage capacity:")
print(garage_price)

# Create bar plot of average price by 
(Content truncated due to size limit. Use line ranges to read in chunks)