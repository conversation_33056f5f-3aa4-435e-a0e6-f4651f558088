#!/usr/bin/env python
# coding: utf-8

"""
DATA PROCESSING AND MODEL BUILDING COMPONENTS
=============================================

This file contains all the data processing, feature engineering, and model building components
extracted from the house price prediction project.

Components included:
1. Missing Value Handler
2. Feature Engineering Pipeline
3. Data Preprocessing Pipeline
4. Model Training and Evaluation
5. Feature Selection and Importance Analysis
6. Model Comparison and Selection
7. Hyperparameter Tuning
8. Model Persistence and Deployment
9. Outlier Detection and Removal
10. Data Validation and Quality Checks

Dependencies: pandas, numpy, sklearn, xgboost, lightgbm, pickle, scipy
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
import pickle

# Machine learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import xgboost as xgb
import lightgbm as lgb

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

class MissingValueHandler:
    """Class for handling missing values in the dataset"""

    def __init__(self, strategy='auto'):
        self.strategy = strategy
        self.imputers = {}
        self.na_means_none_features = [
            'PoolQC', 'MiscFeature', 'Alley', 'Fence', 'FireplaceQu',
            'GarageType', 'GarageFinish', 'GarageQual', 'GarageCond',
            'BsmtQual', 'BsmtCond', 'BsmtExposure', 'BsmtFinType1', 'BsmtFinType2'
        ]

    def handle_missing_values(self, df):
        """Handle missing values based on feature type and domain knowledge"""
        df_processed = df.copy()

        # Features where NA means the feature doesn't exist
        for feature in self.na_means_none_features:
            if feature in df_processed.columns:
                df_processed[feature] = df_processed[feature].fillna('None')

        # Special handling for LotFrontage - impute with neighborhood median
        if 'LotFrontage' in df_processed.columns:
            df_processed['LotFrontage'] = df_processed.groupby('Neighborhood')['LotFrontage'].transform(
                lambda x: x.fillna(x.median()) if not x.median() != x.median() else x.fillna(df_processed['LotFrontage'].median())
            )

        # Fill remaining numerical missing values with 0
        numerical_features = df_processed.select_dtypes(include=['int64', 'float64']).columns
        for feature in numerical_features:
            df_processed[feature] = df_processed[feature].fillna(0)

        # Fill remaining categorical missing values with most frequent value
        categorical_features = df_processed.select_dtypes(include=['object']).columns
        for feature in categorical_features:
            if df_processed[feature].isnull().sum() > 0:
                df_processed[feature] = df_processed[feature].fillna(df_processed[feature].mode()[0])

        return df_processed

    def get_missing_summary(self, df):
        """Get summary of missing values"""
        missing_values = df.isnull().sum()
        missing_percent = (missing_values / len(df)) * 100
        missing_data = pd.concat([missing_values, missing_percent], axis=1)
        missing_data.columns = ['Missing Values', 'Percentage']
        return missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False)

class FeatureEngineer:
    """Class for feature engineering and creation"""

    def __init__(self):
        self.quality_map = {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5}
        self.ordinal_mappings = {
            'ExterQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'ExterCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'BsmtQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'BsmtCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'HeatingQC': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'KitchenQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'FireplaceQu': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'GarageQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'GarageCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'PoolQC': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
            'Fence': {'None': 0, 'MnWw': 1, 'GdWo': 2, 'MnPrv': 3, 'GdPrv': 4},
            'BsmtExposure': {'None': 0, 'No': 1, 'Mn': 2, 'Av': 3, 'Gd': 4},
            'BsmtFinType1': {'None': 0, 'Unf': 1, 'LwQ': 2, 'Rec': 3, 'BLQ': 4, 'ALQ': 5, 'GLQ': 6},
            'BsmtFinType2': {'None': 0, 'Unf': 1, 'LwQ': 2, 'Rec': 3, 'BLQ': 4, 'ALQ': 5, 'GLQ': 6},
            'Functional': {'Sal': 1, 'Sev': 2, 'Maj2': 3, 'Maj1': 4, 'Mod': 5, 'Min2': 6, 'Min1': 7, 'Typ': 8},
            'GarageFinish': {'None': 0, 'Unf': 1, 'RFn': 2, 'Fin': 3},
            'LandSlope': {'Sev': 1, 'Mod': 2, 'Gtl': 3},
            'LotShape': {'IR3': 1, 'IR2': 2, 'IR1': 3, 'Reg': 4},
            'PavedDrive': {'N': 0, 'P': 1, 'Y': 2},
            'Street': {'Grvl': 0, 'Pave': 1},
            'Utilities': {'ELO': 1, 'NoSeWa': 2, 'NoSewr': 3, 'AllPub': 4},
            'CentralAir': {'N': 0, 'Y': 1}
        }

    def create_age_features(self, df):
        """Create age-related features"""
        df_fe = df.copy()

        # House age at time of sale
        df_fe['HouseAge'] = df_fe['YrSold'] - df_fe['YearBuilt']

        # Years since remodeling
        df_fe['RemodAge'] = df_fe['YrSold'] - df_fe['YearRemodAdd']

        # Age of garage at time of sale (if exists)
        df_fe['GarageAge'] = df_fe['YrSold'] - df_fe['GarageYrBlt']
        df_fe.loc[df_fe['GarageAge'] < 0, 'GarageAge'] = 0

        # Is the house recently built (last 5 years)?
        df_fe['IsNew'] = (df_fe['HouseAge'] <= 5).astype(int)

        return df_fe

    def create_area_features(self, df):
        """Create area-related features"""
        df_fe = df.copy()

        # Total square footage
        df_fe['TotalSF'] = df_fe['TotalBsmtSF'] + df_fe['1stFlrSF'] + df_fe['2ndFlrSF']

        # Total bathroom count
        df_fe['TotalBath'] = df_fe['FullBath'] + (0.5 * df_fe['HalfBath']) + df_fe['BsmtFullBath'] + (0.5 * df_fe['BsmtHalfBath'])

        # Total porch area
        df_fe['TotalPorchSF'] = df_fe['OpenPorchSF'] + df_fe['EnclosedPorch'] + df_fe['3SsnPorch'] + df_fe['ScreenPorch']

        # Ratio features
        df_fe['LotRatio'] = df_fe['LotArea'] / df_fe['LotFrontage']
        df_fe['LivingAreaRatio'] = df_fe['GrLivArea'] / df_fe['LotArea']

        # Bathroom to bedroom ratio
        df_fe['BathPerBed'] = df_fe['TotalBath'] / (df_fe['BedroomAbvGr'] + 1)

        return df_fe

    def create_quality_features(self, df):
        """Create quality-related features"""
        df_fe = df.copy()

        # Map quality variables to numerical values
        quality_cols = ['ExterQual', 'ExterCond', 'BsmtQual', 'BsmtCond',
                        'HeatingQC', 'KitchenQual', 'FireplaceQu', 'GarageQual', 'GarageCond']

        for col in quality_cols:
            if col in df_fe.columns:
                df_fe[f'{col}_Num'] = df_fe[col].map(self.quality_map)

        # Create overall quality score
        quality_num_cols = [col + '_Num' for col in quality_cols if col + '_Num' in df_fe.columns]
        if quality_num_cols:
            df_fe['QualityScore'] = df_fe[quality_num_cols].mean(axis=1)

        # Create specific quality scores
        if 'ExterQual_Num' in df_fe.columns and 'ExterCond_Num' in df_fe.columns:
            df_fe['ExterQualScore'] = df_fe[['ExterQual_Num', 'ExterCond_Num']].mean(axis=1)

        if 'BsmtQual_Num' in df_fe.columns and 'BsmtCond_Num' in df_fe.columns:
            df_fe['BsmtQualScore'] = df_fe[['BsmtQual_Num', 'BsmtCond_Num']].mean(axis=1)

        if 'GarageQual_Num' in df_fe.columns and 'GarageCond_Num' in df_fe.columns:
            df_fe['GarageQualScore'] = df_fe[['GarageQual_Num', 'GarageCond_Num']].mean(axis=1)

        return df_fe

    def create_neighborhood_features(self, df, target_col='SalePrice'):
        """Create neighborhood-based features"""
        df_fe = df.copy()

        # Calculate median price by neighborhood
        neighborhood_price = df_fe.groupby('Neighborhood')[target_col].median().reset_index()
        neighborhood_price.columns = ['Neighborhood', 'NeighborhoodMedianPrice']
        df_fe = pd.merge(df_fe, neighborhood_price, on='Neighborhood', how='left')

        # Create neighborhood price categories
        df_fe['NeighborhoodPriceCat'] = pd.qcut(df_fe['NeighborhoodMedianPrice'], 5, labels=False)

        return df_fe

    def create_interaction_features(self, df):
        """Create interaction features"""
        df_fe = df.copy()

        # Interaction between overall quality and total square footage
        df_fe['QualxArea'] = df_fe['OverallQual'] * df_fe['TotalSF']

        # Interaction between overall quality and age
        df_fe['QualxAge'] = df_fe['OverallQual'] * df_fe['HouseAge']

        # Interaction between neighborhood category and total square footage
        df_fe['NeighborhoodCatxArea'] = df_fe['NeighborhoodPriceCat'] * df_fe['TotalSF']

        return df_fe

    def create_polynomial_features(self, df):
        """Create polynomial features for important numerical variables"""
        df_fe = df.copy()

        # Square of important numerical features
        df_fe['OverallQual2'] = df_fe['OverallQual'] ** 2
        df_fe['TotalSF2'] = df_fe['TotalSF'] ** 2
        df_fe['GrLivArea2'] = df_fe['GrLivArea'] ** 2
        df_fe['HouseAge2'] = df_fe['HouseAge'] ** 2

        return df_fe

    def create_binary_indicators(self, df):
        """Create binary indicator features"""
        df_fe = df.copy()

        # Has pool
        df_fe['HasPool'] = (df_fe['PoolArea'] > 0).astype(int)

        # Has garage
        df_fe['HasGarage'] = (df_fe['GarageArea'] > 0).astype(int)

        # Has fireplace
        df_fe['HasFireplace'] = (df_fe['Fireplaces'] > 0).astype(int)

        # Has basement
        df_fe['HasBasement'] = (df_fe['TotalBsmtSF'] > 0).astype(int)

        # Has second floor
        df_fe['Has2ndFloor'] = (df_fe['2ndFlrSF'] > 0).astype(int)

        # Has deck or porch
        df_fe['HasDeckOrPorch'] = ((df_fe['WoodDeckSF'] > 0) | (df_fe['TotalPorchSF'] > 0)).astype(int)

        return df_fe

    def apply_ordinal_encoding(self, df):
        """Apply ordinal encoding to categorical features"""
        df_fe = df.copy()

        for feature, mapping in self.ordinal_mappings.items():
            if feature in df_fe.columns:
                df_fe[feature] = df_fe[feature].map(mapping)

        return df_fe

    def engineer_all_features(self, df, target_col='SalePrice'):
        """Apply all feature engineering steps"""
        print("Starting feature engineering...")

        # Apply all feature engineering steps
        df_fe = self.create_age_features(df)
        df_fe = self.create_area_features(df_fe)
        df_fe = self.create_quality_features(df_fe)
        df_fe = self.create_neighborhood_features(df_fe, target_col)
        df_fe = self.create_interaction_features(df_fe)
        df_fe = self.create_polynomial_features(df_fe)
        df_fe = self.create_binary_indicators(df_fe)
        df_fe = self.apply_ordinal_encoding(df_fe)

        print(f"Feature engineering completed. Shape: {df_fe.shape}")
        return df_fe

class OutlierHandler:
    """Class for outlier detection and removal"""

    def __init__(self, method='iqr'):
        self.method = method
        self.outlier_bounds = {}

    def detect_outliers_iqr(self, df, feature):
        """Detect outliers using IQR method"""
        Q1 = df[feature].quantile(0.25)
        Q3 = df[feature].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        outliers = df[(df[feature] < lower_bound) | (df[feature] > upper_bound)]
        return outliers, lower_bound, upper_bound

    def detect_outliers_zscore(self, df, feature, threshold=3):
        """Detect outliers using Z-score method"""
        z_scores = np.abs(stats.zscore(df[feature]))
        outliers = df[z_scores > threshold]
        return outliers

    def remove_extreme_outliers(self, df, target_col='SalePrice'):
        """Remove extreme outliers based on domain knowledge"""
        df_clean = df.copy()

        # Remove houses with extremely large living area but low price
        df_clean = df_clean.drop(df_clean[(df_clean['GrLivArea'] > 4000) & (df_clean[target_col] < 300000)].index)

        # Remove houses with extremely high price but small living area
        df_clean = df_clean.drop(df_clean[(df_clean['GrLivArea'] < 1000) & (df_clean[target_col] > 400000)].index)

        print(f"Removed {len(df) - len(df_clean)} extreme outliers")
        return df_clean

class DataPreprocessor:
    """Class for data preprocessing pipeline"""

    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_names = None

    def encode_categorical_features(self, df):
        """Encode categorical features using one-hot encoding"""
        df_encoded = df.copy()

        # Get categorical columns
        categorical_cols = df_encoded.select_dtypes(include=['object']).columns.tolist()

        # Apply one-hot encoding
        df_encoded = pd.get_dummies(df_encoded, columns=categorical_cols, drop_first=True)

        return df_encoded

    def scale_features(self, X_train, X_test=None):
        """Scale numerical features"""
        X_train_scaled = self.scaler.fit_transform(X_train)

        if X_test is not None:
            X_test_scaled = self.scaler.transform(X_test)
            return X_train_scaled, X_test_scaled

        return X_train_scaled

    def prepare_data(self, df, target_col='SalePrice', test_size=0.2, random_state=42):
        """Prepare data for modeling"""
        # Separate features and target
        X = df.drop([target_col], axis=1)
        y = df[target_col]

        # Apply log transformation to target
        y_log = np.log1p(y)

        # Encode categorical features
        X_encoded = self.encode_categorical_features(X)

        # Store feature names
        self.feature_names = X_encoded.columns.tolist()

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_encoded, y_log, test_size=test_size, random_state=random_state
        )

        # Scale features
        X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test)

        return X_train_scaled, X_test_scaled, y_train, y_test, X_train, X_test

class ModelTrainer:
    """Class for training and evaluating machine learning models"""

    def __init__(self):
        self.models = {}
        self.model_results = {}

    def initialize_models(self):
        """Initialize different regression models"""
        self.models = {
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0),
            'Lasso Regression': Lasso(alpha=0.001),
            'ElasticNet': ElasticNet(alpha=0.001, l1_ratio=0.5),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42)
        }

    def evaluate_model(self, model, X_train, X_test, y_train, y_test):
        """Evaluate a single model"""
        # Train model
        model.fit(X_train, y_train)

        # Make predictions
        y_train_pred = model.predict(X_train)
        y_test_pred = model.predict(X_test)

        # Calculate metrics on log scale
        train_rmse_log = np.sqrt(mean_squared_error(y_train, y_train_pred))
        test_rmse_log = np.sqrt(mean_squared_error(y_test, y_test_pred))
        train_mae_log = mean_absolute_error(y_train, y_train_pred)
        test_mae_log = mean_absolute_error(y_test, y_test_pred)
        train_r2_log = r2_score(y_train, y_train_pred)
        test_r2_log = r2_score(y_test, y_test_pred)

        # Convert back to original scale
        y_train_orig = np.expm1(y_train)
        y_test_orig = np.expm1(y_test)
        y_train_pred_orig = np.expm1(y_train_pred)
        y_test_pred_orig = np.expm1(y_test_pred)

        # Calculate metrics on original scale
        train_rmse_orig = np.sqrt(mean_squared_error(y_train_orig, y_train_pred_orig))
        test_rmse_orig = np.sqrt(mean_squared_error(y_test_orig, y_test_pred_orig))
        train_mae_orig = mean_absolute_error(y_train_orig, y_train_pred_orig)
        test_mae_orig = mean_absolute_error(y_test_orig, y_test_pred_orig)
        train_r2_orig = r2_score(y_train_orig, y_train_pred_orig)
        test_r2_orig = r2_score(y_test_orig, y_test_pred_orig)

        return {
            'train_rmse_log': train_rmse_log,
            'test_rmse_log': test_rmse_log,
            'train_mae_log': train_mae_log,
            'test_mae_log': test_mae_log,
            'train_r2_log': train_r2_log,
            'test_r2_log': test_r2_log,
            'train_rmse_orig': train_rmse_orig,
            'test_rmse_orig': test_rmse_orig,
            'train_mae_orig': train_mae_orig,
            'test_mae_orig': test_mae_orig,
            'train_r2_orig': train_r2_orig,
            'test_r2_orig': test_r2_orig
        }

    def train_all_models(self, X_train, X_test, y_train, y_test):
        """Train and evaluate all models"""
        self.initialize_models()

        print("Training and evaluating models...")
        for name, model in self.models.items():
            print(f"Training {name}...")
            results = self.evaluate_model(model, X_train, X_test, y_train, y_test)
            self.model_results[name] = results

        return self.model_results

    def get_best_model(self, metric='test_rmse_orig'):
        """Get the best performing model based on specified metric"""
        if not self.model_results:
            raise ValueError("No models have been trained yet.")

        best_model_name = min(self.model_results.keys(),
                             key=lambda x: self.model_results[x][metric])
        best_model = self.models[best_model_name]
        best_score = self.model_results[best_model_name][metric]

        return best_model_name, best_model, best_score

    def plot_model_comparison(self, reports_dir='reports'):
        """Plot model comparison"""
        if not self.model_results:
            raise ValueError("No models have been trained yet.")

        # Create comparison DataFrame
        comparison_data = []
        for model_name, results in self.model_results.items():
            comparison_data.append({
                'Model': model_name,
                'Test RMSE (Original)': results['test_rmse_orig'],
                'Test R² (Original)': results['test_r2_orig'],
                'Test MAE (Original)': results['test_mae_orig']
            })

        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('Test RMSE (Original)')

        # Plot comparison
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # RMSE comparison
        sns.barplot(x='Test RMSE (Original)', y='Model', data=comparison_df, ax=axes[0])
        axes[0].set_title('Model Comparison - RMSE (Lower is Better)')

        # R² comparison
        sns.barplot(x='Test R² (Original)', y='Model', data=comparison_df, ax=axes[1])
        axes[1].set_title('Model Comparison - R² (Higher is Better)')

        # MAE comparison
        sns.barplot(x='Test MAE (Original)', y='Model', data=comparison_df, ax=axes[2])
        axes[2].set_title('Model Comparison - MAE (Lower is Better)')

        plt.tight_layout()
        os.makedirs(reports_dir, exist_ok=True)
        plt.savefig(f'{reports_dir}/model_comparison.png')
        plt.close()

        return comparison_df

class FeatureSelector:
    """Class for feature selection and importance analysis"""

    def __init__(self):
        self.selected_features = None
        self.feature_importance = None

    def select_features_lasso(self, X, y, alpha=0.001):
        """Select features using Lasso regularization"""
        lasso = Lasso(alpha=alpha)
        lasso.fit(X, y)

        # Get selected features (non-zero coefficients)
        selected_mask = lasso.coef_ != 0
        self.selected_features = X.columns[selected_mask].tolist()

        # Get feature importance
        self.feature_importance = pd.DataFrame({
            'feature': X.columns[selected_mask],
            'importance': np.abs(lasso.coef_[selected_mask])
        }).sort_values('importance', ascending=False)

        print(f"Selected {len(self.selected_features)} features out of {X.shape[1]}")
        return self.selected_features, self.feature_importance

    def get_feature_importance_rf(self, model, feature_names):
        """Get feature importance from Random Forest model"""
        if hasattr(model, 'feature_importances_'):
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)

            return importance_df
        else:
            print("Model does not have feature_importances_ attribute")
            return None

    def plot_feature_importance(self, importance_df, top_n=20, reports_dir='reports'):
        """Plot feature importance"""
        plt.figure(figsize=(10, 8))
        top_features = importance_df.head(top_n)
        sns.barplot(x='importance', y='feature', data=top_features)
        plt.title(f'Top {top_n} Feature Importance')
        plt.xlabel('Importance')
        plt.tight_layout()
        os.makedirs(reports_dir, exist_ok=True)
        plt.savefig(f'{reports_dir}/feature_importance.png')
        plt.close()

class ModelPersistence:
    """Class for saving and loading trained models"""

    def __init__(self, models_dir='models'):
        self.models_dir = models_dir
        os.makedirs(models_dir, exist_ok=True)

    def save_model(self, model, model_name, scaler=None):
        """Save trained model and scaler"""
        model_path = os.path.join(self.models_dir, f'{model_name}_model.pkl')
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)

        if scaler is not None:
            scaler_path = os.path.join(self.models_dir, f'{model_name}_scaler.pkl')
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)

        print(f"Model saved to {model_path}")

    def load_model(self, model_name):
        """Load trained model and scaler"""
        model_path = os.path.join(self.models_dir, f'{model_name}_model.pkl')
        scaler_path = os.path.join(self.models_dir, f'{model_name}_scaler.pkl')

        with open(model_path, 'rb') as f:
            model = pickle.load(f)

        scaler = None
        if os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                scaler = pickle.load(f)

        return model, scaler

    def save_results(self, results, filename='model_results.pkl'):
        """Save model results"""
        results_path = os.path.join(self.models_dir, filename)
        with open(results_path, 'wb') as f:
            pickle.dump(results, f)
        print(f"Results saved to {results_path}")

class PredictionVisualizer:
    """Class for visualizing model predictions"""

    def __init__(self, reports_dir='reports'):
        self.reports_dir = reports_dir
        os.makedirs(reports_dir, exist_ok=True)

    def plot_predictions(self, y_true, y_pred, model_name, scale='original'):
        """Plot actual vs predicted values"""
        plt.figure(figsize=(10, 8))

        # Convert to original scale if needed
        if scale == 'original':
            y_true_plot = np.expm1(y_true) if y_true.max() < 20 else y_true
            y_pred_plot = np.expm1(y_pred) if y_pred.max() < 20 else y_pred
        else:
            y_true_plot = y_true
            y_pred_plot = y_pred

        # Create scatter plot
        plt.scatter(y_true_plot, y_pred_plot, alpha=0.6)

        # Add perfect prediction line
        min_val = min(y_true_plot.min(), y_pred_plot.min())
        max_val = max(y_true_plot.max(), y_pred_plot.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

        # Calculate R²
        r2 = r2_score(y_true_plot, y_pred_plot)

        plt.xlabel('Actual Values')
        plt.ylabel('Predicted Values')
        plt.title(f'{model_name} - Actual vs Predicted (R² = {r2:.3f})')
        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/{model_name}_predictions.png')
        plt.close()

    def plot_residuals(self, y_true, y_pred, model_name):
        """Plot residuals"""
        residuals = y_true - y_pred

        plt.figure(figsize=(12, 5))

        # Residuals vs predicted
        plt.subplot(1, 2, 1)
        plt.scatter(y_pred, residuals, alpha=0.6)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Values')
        plt.ylabel('Residuals')
        plt.title(f'{model_name} - Residuals vs Predicted')

        # Residuals histogram
        plt.subplot(1, 2, 2)
        plt.hist(residuals, bins=30, alpha=0.7)
        plt.xlabel('Residuals')
        plt.ylabel('Frequency')
        plt.title(f'{model_name} - Residuals Distribution')

        plt.tight_layout()
        plt.savefig(f'{self.reports_dir}/{model_name}_residuals.png')
        plt.close()

# Example usage function
def run_complete_pipeline(df, target_col='SalePrice', reports_dir='reports', models_dir='models'):
    """
    Run complete data processing and modeling pipeline

    Args:
        df: DataFrame with house price data
        target_col: Name of target variable column
        reports_dir: Directory to save reports and visualizations
        models_dir: Directory to save trained models

    Returns:
        Dictionary containing all pipeline results
    """
    results = {}

    print("Starting complete ML pipeline...")

    # 1. Handle missing values
    print("\n1. Handling missing values...")
    missing_handler = MissingValueHandler()
    df_clean = missing_handler.handle_missing_values(df)
    results['missing_summary'] = missing_handler.get_missing_summary(df)

    # 2. Feature engineering
    print("\n2. Feature engineering...")
    feature_engineer = FeatureEngineer()
    df_engineered = feature_engineer.engineer_all_features(df_clean, target_col)

    # 3. Remove outliers
    print("\n3. Removing outliers...")
    outlier_handler = OutlierHandler()
    df_no_outliers = outlier_handler.remove_extreme_outliers(df_engineered, target_col)

    # 4. Prepare data for modeling
    print("\n4. Preparing data for modeling...")
    preprocessor = DataPreprocessor()
    X_train_scaled, X_test_scaled, y_train, y_test, X_train, X_test = preprocessor.prepare_data(
        df_no_outliers, target_col
    )

    # 5. Train models
    print("\n5. Training models...")
    trainer = ModelTrainer()
    model_results = trainer.train_all_models(X_train_scaled, X_test_scaled, y_train, y_test)
    results['model_results'] = model_results

    # 6. Get best model
    best_model_name, best_model, best_score = trainer.get_best_model()
    print(f"\nBest model: {best_model_name} with RMSE: {best_score:.2f}")
    results['best_model'] = {'name': best_model_name, 'model': best_model, 'score': best_score}

    # 7. Plot model comparison
    comparison_df = trainer.plot_model_comparison(reports_dir)
    results['model_comparison'] = comparison_df

    # 8. Feature importance analysis
    print("\n6. Analyzing feature importance...")
    feature_selector = FeatureSelector()
    if hasattr(best_model, 'feature_importances_'):
        importance_df = feature_selector.get_feature_importance_rf(best_model, preprocessor.feature_names)
        feature_selector.plot_feature_importance(importance_df, reports_dir=reports_dir)
        results['feature_importance'] = importance_df

    # 9. Save models
    print("\n7. Saving models...")
    model_persistence = ModelPersistence(models_dir)
    model_persistence.save_model(best_model, best_model_name, preprocessor.scaler)
    model_persistence.save_results(results)

    # 10. Visualize predictions
    print("\n8. Creating prediction visualizations...")
    visualizer = PredictionVisualizer(reports_dir)
    y_test_pred = best_model.predict(X_test_scaled)
    visualizer.plot_predictions(y_test, y_test_pred, best_model_name)
    visualizer.plot_residuals(y_test, y_test_pred, best_model_name)

    print("\nComplete ML pipeline finished!")
    return results