#!/usr/bin/env python
# coding: utf-8

# # <PERSON> Price Prediction - Feature Engineering
# 
# This notebook implements creative feature engineering based on insights from the exploratory data analysis.

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import <PERSON><PERSON>ncoder, StandardScaler
from sklearn.impute import SimpleImputer
import warnings
import os

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

# Load the processed data
df = pd.read_csv('../data/processed_data.csv')
print(f"Dataset shape: {df.shape}")

# Make a copy to avoid modifying the original data
df_fe = df.copy()

# -----------------------------------------------------------------------------
# 1. Handle Missing Values
# -----------------------------------------------------------------------------
print("\n--- Handling Missing Values ---")

# Features where NA means the feature doesn't exist
na_means_none_features = [
    'PoolQC', 'MiscFeature', 'Alley', 'Fence', 'FireplaceQu',
    'GarageType', 'GarageFinish', 'GarageQual', 'GarageCond',
    'BsmtQual', 'BsmtCond', 'BsmtExposure', 'BsmtFinType1', 'BsmtFinType2'
]

# Replace NA with 'None' for categorical features where NA means the feature doesn't exist
for feature in na_means_none_features:
    if feature in df_fe.columns:
        df_fe[feature] = df_fe[feature].fillna('None')

# Impute LotFrontage with median of neighborhood
df_fe['LotFrontage'] = df_fe.groupby('Neighborhood')['LotFrontage'].transform(
    lambda x: x.fillna(x.median()) if not x.median() != x.median() else x.fillna(df_fe['LotFrontage'].median())
)

# Fill remaining numerical missing values with 0
numerical_features = df_fe.select_dtypes(include=['int64', 'float64']).columns
for feature in numerical_features:
    df_fe[feature] = df_fe[feature].fillna(0)

# Fill remaining categorical missing values with most frequent value
categorical_features = df_fe.select_dtypes(include=['object']).columns
for feature in categorical_features:
    df_fe[feature] = df_fe[feature].fillna(df_fe[feature].mode()[0])

# Check if any missing values remain
missing_after = df_fe.isnull().sum().sum()
print(f"Missing values after imputation: {missing_after}")

# -----------------------------------------------------------------------------
# 2. Create Age-Related Features
# -----------------------------------------------------------------------------
print("\n--- Creating Age-Related Features ---")

# House age at time of sale
df_fe['HouseAge'] = df_fe['YrSold'] - df_fe['YearBuilt']

# Years since remodeling
df_fe['RemodAge'] = df_fe['YrSold'] - df_fe['YearRemodAdd']

# Age of garage at time of sale (if exists)
df_fe['GarageAge'] = df_fe['YrSold'] - df_fe['GarageYrBlt']
df_fe.loc[df_fe['GarageAge'] < 0, 'GarageAge'] = 0  # Fix for garages built after house

# Is the house recently built (last 5 years)?
df_fe['IsNew'] = (df_fe['HouseAge'] <= 5).astype(int)

# -----------------------------------------------------------------------------
# 3. Create Area-Related Features
# -----------------------------------------------------------------------------
print("\n--- Creating Area-Related Features ---")

# Total square footage
df_fe['TotalSF'] = df_fe['TotalBsmtSF'] + df_fe['1stFlrSF'] + df_fe['2ndFlrSF']

# Total bathroom count
df_fe['TotalBath'] = df_fe['FullBath'] + (0.5 * df_fe['HalfBath']) + df_fe['BsmtFullBath'] + (0.5 * df_fe['BsmtHalfBath'])

# Total porch area
df_fe['TotalPorchSF'] = df_fe['OpenPorchSF'] + df_fe['EnclosedPorch'] + df_fe['3SsnPorch'] + df_fe['ScreenPorch']

# Ratio features
df_fe['LotRatio'] = df_fe['LotArea'] / df_fe['LotFrontage']
df_fe['LivingAreaRatio'] = df_fe['GrLivArea'] / df_fe['LotArea']
df_fe['PricePerSF'] = df_fe['SalePrice'] / df_fe['TotalSF']

# Bathroom to bedroom ratio
df_fe['BathPerBed'] = df_fe['TotalBath'] / (df_fe['BedroomAbvGr'] + 1)  # Adding 1 to avoid division by zero

# -----------------------------------------------------------------------------
# 4. Create Quality-Related Features
# -----------------------------------------------------------------------------
print("\n--- Creating Quality-Related Features ---")

# Map quality variables to numerical values
quality_map = {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5}

quality_cols = ['ExterQual', 'ExterCond', 'BsmtQual', 'BsmtCond', 
                'HeatingQC', 'KitchenQual', 'FireplaceQu', 'GarageQual', 'GarageCond']

for col in quality_cols:
    if col in df_fe.columns:
        df_fe[f'{col}_Num'] = df_fe[col].map(quality_map)

# Create overall quality score
df_fe['QualityScore'] = df_fe[[col + '_Num' for col in quality_cols if col + '_Num' in df_fe.columns]].mean(axis=1)

# Create exterior quality score
df_fe['ExterQualScore'] = df_fe[['ExterQual_Num', 'ExterCond_Num']].mean(axis=1)

# Create basement quality score
if 'BsmtQual_Num' in df_fe.columns and 'BsmtCond_Num' in df_fe.columns:
    df_fe['BsmtQualScore'] = df_fe[['BsmtQual_Num', 'BsmtCond_Num']].mean(axis=1)

# Create garage quality score
if 'GarageQual_Num' in df_fe.columns and 'GarageCond_Num' in df_fe.columns:
    df_fe['GarageQualScore'] = df_fe[['GarageQual_Num', 'GarageCond_Num']].mean(axis=1)

# -----------------------------------------------------------------------------
# 5. Create Neighborhood Features
# -----------------------------------------------------------------------------
print("\n--- Creating Neighborhood Features ---")

# Calculate median price by neighborhood
neighborhood_price = df_fe.groupby('Neighborhood')['SalePrice'].median().reset_index()
neighborhood_price.columns = ['Neighborhood', 'NeighborhoodMedianPrice']
df_fe = pd.merge(df_fe, neighborhood_price, on='Neighborhood', how='left')

# Create neighborhood price categories
df_fe['NeighborhoodPriceCat'] = pd.qcut(df_fe['NeighborhoodMedianPrice'], 5, labels=False)

# -----------------------------------------------------------------------------
# 6. Create Interaction Features
# -----------------------------------------------------------------------------
print("\n--- Creating Interaction Features ---")

# Interaction between overall quality and total square footage
df_fe['QualxArea'] = df_fe['OverallQual'] * df_fe['TotalSF']

# Interaction between overall quality and age
df_fe['QualxAge'] = df_fe['OverallQual'] * df_fe['HouseAge']

# Interaction between neighborhood category and total square footage
df_fe['NeighborhoodCatxArea'] = df_fe['NeighborhoodPriceCat'] * df_fe['TotalSF']

# -----------------------------------------------------------------------------
# 7. Create Polynomial Features for Important Numerical Variables
# -----------------------------------------------------------------------------
print("\n--- Creating Polynomial Features ---")

# Square of important numerical features
df_fe['OverallQual2'] = df_fe['OverallQual'] ** 2
df_fe['TotalSF2'] = df_fe['TotalSF'] ** 2
df_fe['GrLivArea2'] = df_fe['GrLivArea'] ** 2
df_fe['HouseAge2'] = df_fe['HouseAge'] ** 2

# -----------------------------------------------------------------------------
# 8. Create Binary Indicators
# -----------------------------------------------------------------------------
print("\n--- Creating Binary Indicators ---")

# Has pool
df_fe['HasPool'] = (df_fe['PoolArea'] > 0).astype(int)

# Has garage
df_fe['HasGarage'] = (df_fe['GarageArea'] > 0).astype(int)

# Has fireplace
df_fe['HasFireplace'] = (df_fe['Fireplaces'] > 0).astype(int)

# Has basement
df_fe['HasBasement'] = (df_fe['TotalBsmtSF'] > 0).astype(int)

# Has second floor
df_fe['Has2ndFloor'] = (df_fe['2ndFlrSF'] > 0).astype(int)

# Has deck or porch
df_fe['HasDeckOrPorch'] = ((df_fe['WoodDeckSF'] > 0) | (df_fe['TotalPorchSF'] > 0)).astype(int)

# -----------------------------------------------------------------------------
# 9. Encode Categorical Variables
# -----------------------------------------------------------------------------
print("\n--- Encoding Categorical Variables ---")

# Identify categorical columns
categorical_cols = df_fe.select_dtypes(include=['object']).columns.tolist()
print(f"Number of categorical columns: {len(categorical_cols)}")

# Use label encoding for ordinal categorical features
ordinal_features = ['ExterQual', 'ExterCond', 'BsmtQual', 'BsmtCond', 
                    'HeatingQC', 'KitchenQual', 'FireplaceQu', 'GarageQual', 'GarageCond',
                    'PoolQC', 'Fence', 'BsmtExposure', 'BsmtFinType1', 'BsmtFinType2',
                    'Functional', 'GarageFinish', 'LandSlope', 'LotShape', 'PavedDrive',
                    'Street', 'Utilities', 'CentralAir']

# Define ordinal mappings
ordinal_mappings = {
    'ExterQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'ExterCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'BsmtQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'BsmtCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'HeatingQC': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'KitchenQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'FireplaceQu': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'GarageQual': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'GarageCond': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'PoolQC': {'None': 0, 'Po': 1, 'Fa': 2, 'TA': 3, 'Gd': 4, 'Ex': 5},
    'Fence': {'None': 0, 'MnWw': 1, 'GdWo': 2, 'MnPrv': 3, 'GdPrv': 4},
    'BsmtExposure': {'None': 0, 'No': 1, 'Mn': 2, 'Av': 3, 'Gd': 4},
    'BsmtFinType1': {'None': 0, 'Unf': 1, 'LwQ': 2, 'Rec': 3, 'BLQ': 4, 'ALQ': 5, 'GLQ': 6},
    'BsmtFinType2': {'None': 0, 'Unf': 1, 'LwQ': 2, 'Rec': 3, 'BLQ': 4, 'ALQ': 5, 'GLQ': 6},
    'Functional': {'Sal': 1, 'Sev': 2, 'Maj2': 3, 'Maj1': 4, 'Mod': 5, 'Min2': 6, 'Min1': 7, 'Typ': 8},
    'GarageFinish': {'None': 0, 'Unf': 1, 'RFn': 2, 'Fin': 3},
    'LandSlope': {'Sev': 1, 'Mod': 2, 'Gtl': 3},
    'LotShape': {'IR3': 1, 'IR2': 2, 'IR1': 3, 'Reg': 4},
    'PavedDrive': {'N': 0, 'P': 1, 'Y': 2},
    'Street': {'Grvl': 0, 'Pave': 1},
    'Utilities': {'ELO': 1, 'NoSeWa': 2, 'NoSewr': 3, 'AllPub': 4},
    'CentralAir': {'N': 0, 'Y': 1}
}

# Apply ordinal mappings
for feature in ordinal_features:
    if feature in df_fe.columns and feature in ordinal_mappings:
        df_fe[feature] = df_fe[feature].map(ordinal_mappings[feature])

# Use one-hot encoding for nominal categorical features
nominal_features = [col for col in categorical_cols if col not in ordinal_features]
df_fe = pd.get_dummies(df_fe, columns=nominal_features, drop_first=True)

# -----------------------------------------------------------------------------
# 10. Handle Outliers
# -----------------------------------------------------------------------------
print("\n--- Handling Outliers ---")

# Identify and remove extreme outliers in GrLivArea
plt.figure(figsize=(10, 6))
sns.scatterplot(x='GrLivArea', y='SalePrice', data=df_fe)
plt.title('SalePrice vs GrLivArea - Before Outlier Removal')
plt.savefig('../reports/outliers_before_removal.png')
plt.close()

# Remove houses with very large living area but low price
outliers = df_fe[(df_fe['GrLivArea'] > 4000) & (df_fe['SalePrice'] < 300000)].index
df_fe_no_outliers = df_fe.drop(outliers)

plt.figure(figsize=(10, 6))
sns.scatterplot(x='GrLivArea', y='SalePrice', data=df_fe_no_outliers)
plt.title('SalePrice vs GrLivArea - After Outlier Removal')
plt.savefig('../reports/outliers_after_removal.png')
plt.close()

print(f"Number of outliers removed: {len(outliers)}")
print(f"Dataset shape after outlier removal: {df_fe_no_outliers.shape}")

# -----------------------------------------------------------------------------
# 11. Feature Selection
# -----------------------------------------------------------------------------
print("\n--- Feature Selection ---")

# Calculate correlation with SalePrice
correlation = df_fe_no_outliers.corr()['SalePrice'].sort_values(ascending=False)
print("\nTop 20 features correlated with SalePrice:")
print(correlation.head(20))

# Save top correlated features
top_correlated = correlation.head(30).index.tolist()
with open('../reports/top_correlated_features.txt', 'w') as f:
    for feature in top_correlated:
        f.write(f"{feature}: {correlation[feature]}\n")

# -----------------------------------------------------------------------------
# 12. Save Engineered Dataset
# -----------------------------------------------------------------------------
# Save the dataset with all engineered features
df_fe.to_csv('../data/engineered_data_with_outliers.csv', index=False)
df_fe_no_outliers.to_csv('../data/engineered_data.csv', index=False)

print("\nFeature engineering completed. Engineered datasets saved to data directory.")
print(f"Original dataset shape: {df.shape}")
print(f"Engineered dataset shape (with outliers): {df_fe.shape}")
print(f"Engineered dataset shape (without outliers): {df_fe_no_outliers.shape}")
print(f"Number of new features created: {df_fe.shape[1] - df.shape[1]}")
