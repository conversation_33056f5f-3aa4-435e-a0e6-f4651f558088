#!/usr/bin/env python
# coding: utf-8

# # House Price Prediction - Exploratory Data Analysis
# 
# This notebook performs exploratory data analysis on the Ames Housing dataset to understand the data structure, identify patterns, and prepare for feature engineering and modeling.

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os

# Create reports directory if it doesn't exist
os.makedirs('../reports', exist_ok=True)

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

# Load the dataset
df = pd.read_csv('../data/data.csv')

# Display basic information
print("Dataset shape:", df.shape)
print("\nFirst 5 rows:")
print(df.head())

# Check data types and missing values
print("\nData types and non-null counts:")
print(df.info())

# Summary statistics
print("\nSummary statistics for numerical features:")
print(df.describe())

# Check for missing values
missing_values = df.isnull().sum()
missing_percent = (missing_values / len(df)) * 100
missing_data = pd.concat([missing_values, missing_percent], axis=1)
missing_data.columns = ['Missing Values', 'Percentage']
print("\nMissing values analysis:")
print(missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False))

# Analyze the target variable (SalePrice)
print("\nSalePrice statistics:")
print(df['SalePrice'].describe())

plt.figure(figsize=(10, 6))
sns.histplot(df['SalePrice'], kde=True)
plt.title('Distribution of Sale Price')
plt.savefig('../reports/saleprice_distribution.png')
plt.close()

# Check for skewness in SalePrice
print("\nSkewness of SalePrice:", df['SalePrice'].skew())

# Log transform SalePrice to handle skewness
df['LogSalePrice'] = np.log1p(df['SalePrice'])
plt.figure(figsize=(10, 6))
sns.histplot(df['LogSalePrice'], kde=True)
plt.title('Distribution of Log-Transformed Sale Price')
plt.savefig('../reports/log_saleprice_distribution.png')
plt.close()

# Correlation analysis for numerical features
numeric_features = df.select_dtypes(include=[np.number])
correlation = numeric_features.corr()

# Top 10 features correlated with SalePrice
print("\nTop 10 features correlated with SalePrice:")
print(correlation['SalePrice'].sort_values(ascending=False).head(11))

# Correlation heatmap - fixed to use only numeric columns
plt.figure(figsize=(12, 10))
top_corr_features = correlation['SalePrice'].sort_values(ascending=False).head(11).index
top_corr_matrix = correlation.loc[top_corr_features, top_corr_features]
sns.heatmap(top_corr_matrix, annot=True, cmap='coolwarm', linewidths=0.5)
plt.title('Correlation Heatmap of Top Features')
plt.savefig('../reports/correlation_heatmap.png')
plt.close()

# Analyze categorical features
categorical_features = df.select_dtypes(include=['object'])
print("\nCategorical features in the dataset:")
print(categorical_features.columns.tolist())

# Analyze relationship between important categorical features and SalePrice
important_cat_features = ['Neighborhood', 'ExterQual', 'KitchenQual', 'BsmtQual', 'GarageType', 'SaleCondition']

for feature in important_cat_features:
    if feature in df.columns:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x=feature, y='SalePrice', data=df)
        plt.title(f'SalePrice by {feature}')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'../reports/saleprice_by_{feature}.png')
        plt.close()

# Analyze relationship between important numerical features and SalePrice
important_num_features = ['OverallQual', 'GrLivArea', 'GarageCars', 'TotalBsmtSF', 'YearBuilt']

for feature in important_num_features:
    if feature in df.columns:
        plt.figure(figsize=(10, 6))
        sns.scatterplot(x=feature, y='SalePrice', data=df)
        plt.title(f'SalePrice vs {feature}')
        plt.savefig(f'../reports/saleprice_vs_{feature}.png')
        plt.close()

# Check for outliers in GrLivArea
plt.figure(figsize=(10, 6))
sns.scatterplot(x='GrLivArea', y='SalePrice', data=df)
plt.title('SalePrice vs GrLivArea (Living Area)')
plt.savefig('../reports/outliers_GrLivArea.png')
plt.close()

# Save processed data for further analysis
df.to_csv('../data/processed_data.csv', index=False)

print("Exploratory Data Analysis completed. Results saved to reports directory.")
