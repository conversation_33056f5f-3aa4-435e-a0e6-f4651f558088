#!/usr/bin/env python
# coding: utf-8

# # House Price Prediction - Model Building and Evaluation
# 
# This notebook builds and evaluates various regression models for predicting house prices using the engineered features.

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
import pickle

# Machine learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

# Create models directory if it doesn't exist
os.makedirs('../models', exist_ok=True)
os.makedirs('../reports', exist_ok=True)

# Load the engineered data (without outliers)
df = pd.read_csv('../data/engineered_data.csv')
print(f"Dataset shape: {df.shape}")

# -----------------------------------------------------------------------------
# 1. Prepare Data for Modeling
# -----------------------------------------------------------------------------
print("\n--- Preparing Data for Modeling ---")

# Define target variable and features
y = df['SalePrice']
y_log = np.log1p(y)  # Log-transformed target for better model performance
X = df.drop(['SalePrice', 'LogSalePrice', 'PricePerSF'], axis=1)  # Remove price-related columns

# Print feature information
print(f"Number of features: {X.shape[1]}")

# Split data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y_log, test_size=0.2, random_state=42)
print(f"Training set shape: {X_train.shape}")
print(f"Testing set shape: {X_test.shape}")

# Scale numerical features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# -----------------------------------------------------------------------------
# 2. Define Evaluation Metrics
# -----------------------------------------------------------------------------
def evaluate_model(model, X_train, X_test, y_train, y_test, model_name):
    """
    Evaluate model performance on training and testing data
    """
    # Make predictions
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    
    # Convert log predictions back to original scale
    y_train_pred_exp = np.expm1(y_train_pred)
    y_test_pred_exp = np.expm1(y_test_pred)
    y_train_exp = np.expm1(y_train)
    y_test_exp = np.expm1(y_test)
    
    # Calculate metrics on log scale
    train_rmse_log = np.sqrt(mean_squared_error(y_train, y_train_pred))
    test_rmse_log = np.sqrt(mean_squared_error(y_test, y_test_pred))
    train_mae_log = mean_absolute_error(y_train, y_train_pred)
    test_mae_log = mean_absolute_error(y_test, y_test_pred)
    train_r2_log = r2_score(y_train, y_train_pred)
    test_r2_log = r2_score(y_test, y_test_pred)
    
    # Calculate metrics on original scale
    train_rmse = np.sqrt(mean_squared_error(y_train_exp, y_train_pred_exp))
    test_rmse = np.sqrt(mean_squared_error(y_test_exp, y_test_pred_exp))
    train_mae = mean_absolute_error(y_train_exp, y_train_pred_exp)
    test_mae = mean_absolute_error(y_test_exp, y_test_pred_exp)
    train_r2 = r2_score(y_train_exp, y_train_pred_exp)
    test_r2 = r2_score(y_test_exp, y_test_pred_exp)
    
    # Store results
    results = {
        'Model': model_name,
        'Train RMSE (log)': train_rmse_log,
        'Test RMSE (log)': test_rmse_log,
        'Train MAE (log)': train_mae_log,
        'Test MAE (log)': test_mae_log,
        'Train R² (log)': train_r2_log,
        'Test R² (log)': test_r2_log,
        'Train RMSE': train_rmse,
        'Test RMSE': test_rmse,
        'Train MAE': train_mae,
        'Test MAE': test_mae,
        'Train R²': train_r2,
        'Test R²': test_r2
    }
    
    return results, y_test_pred_exp

# Function to plot actual vs predicted values
def plot_predictions(y_true, y_pred, model_name):
    plt.figure(figsize=(10, 6))
    plt.scatter(y_true, y_pred, alpha=0.5)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--')
    plt.xlabel('Actual Prices')
    plt.ylabel('Predicted Prices')
    plt.title(f'{model_name}: Actual vs Predicted House Prices')
    plt.savefig(f'../reports/{model_name.replace(" ", "_").lower()}_predictions.png')
    plt.close()

# -----------------------------------------------------------------------------
# 3. Build and Evaluate Models
# -----------------------------------------------------------------------------
print("\n--- Building and Evaluating Models ---")

# Initialize results dataframe
results_df = pd.DataFrame()

# 3.1 Linear Regression (Baseline)
print("\nTraining Linear Regression...")
lr = LinearRegression()
lr.fit(X_train_scaled, y_train)
lr_results, lr_preds = evaluate_model(lr, X_train_scaled, X_test_scaled, y_train, y_test, "Linear Regression")
results_df = pd.concat([results_df, pd.DataFrame([lr_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), lr_preds, "Linear Regression")

# Save model
with open('../models/linear_regression.pkl', 'wb') as f:
    pickle.dump(lr, f)

# 3.2 Ridge Regression
print("\nTraining Ridge Regression...")
ridge = Ridge(alpha=10.0)
ridge.fit(X_train_scaled, y_train)
ridge_results, ridge_preds = evaluate_model(ridge, X_train_scaled, X_test_scaled, y_train, y_test, "Ridge Regression")
results_df = pd.concat([results_df, pd.DataFrame([ridge_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), ridge_preds, "Ridge Regression")

# Save model
with open('../models/ridge_regression.pkl', 'wb') as f:
    pickle.dump(ridge, f)

# 3.3 Lasso Regression
print("\nTraining Lasso Regression...")
lasso = Lasso(alpha=0.001)
lasso.fit(X_train_scaled, y_train)
lasso_results, lasso_preds = evaluate_model(lasso, X_train_scaled, X_test_scaled, y_train, y_test, "Lasso Regression")
results_df = pd.concat([results_df, pd.DataFrame([lasso_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), lasso_preds, "Lasso Regression")

# Save model
with open('../models/lasso_regression.pkl', 'wb') as f:
    pickle.dump(lasso, f)

# 3.4 ElasticNet
print("\nTraining ElasticNet...")
elastic = ElasticNet(alpha=0.001, l1_ratio=0.5)
elastic.fit(X_train_scaled, y_train)
elastic_results, elastic_preds = evaluate_model(elastic, X_train_scaled, X_test_scaled, y_train, y_test, "ElasticNet")
results_df = pd.concat([results_df, pd.DataFrame([elastic_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), elastic_preds, "ElasticNet")

# Save model
with open('../models/elasticnet.pkl', 'wb') as f:
    pickle.dump(elastic, f)

# 3.5 Random Forest
print("\nTraining Random Forest...")
rf = RandomForestRegressor(n_estimators=100, random_state=42)
rf.fit(X_train, y_train)
rf_results, rf_preds = evaluate_model(rf, X_train, X_test, y_train, y_test, "Random Forest")
results_df = pd.concat([results_df, pd.DataFrame([rf_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), rf_preds, "Random Forest")

# Save model
with open('../models/random_forest.pkl', 'wb') as f:
    pickle.dump(rf, f)

# 3.6 Gradient Boosting
print("\nTraining Gradient Boosting...")
gb = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42)
gb.fit(X_train, y_train)
gb_results, gb_preds = evaluate_model(gb, X_train, X_test, y_train, y_test, "Gradient Boosting")
results_df = pd.concat([results_df, pd.DataFrame([gb_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), gb_preds, "Gradient Boosting")

# Save model
with open('../models/gradient_boosting.pkl', 'wb') as f:
    pickle.dump(gb, f)

# 3.7 XGBoost
print("\nTraining XGBoost...")
xgb_model = xgb.XGBRegressor(n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42)
xgb_model.fit(X_train, y_train)
xgb_results, xgb_preds = evaluate_model(xgb_model, X_train, X_test, y_train, y_test, "XGBoost")
results_df = pd.concat([results_df, pd.DataFrame([xgb_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), xgb_preds, "XGBoost")

# Save model
with open('../models/xgboost.pkl', 'wb') as f:
    pickle.dump(xgb_model, f)

# 3.8 LightGBM
print("\nTraining LightGBM...")
lgb_model = lgb.LGBMRegressor(n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42)
lgb_model.fit(X_train, y_train)
lgb_results, lgb_preds = evaluate_model(lgb_model, X_train, X_test, y_train, y_test, "LightGBM")
results_df = pd.concat([results_df, pd.DataFrame([lgb_results])], ignore_index=True)
plot_predictions(np.expm1(y_test), lgb_preds, "LightGBM")

# Save model
with open('../models/lightgbm.pkl', 'wb') as f:
    pickle.dump(lgb_model, f)

# -----------------------------------------------------------------------------
# 4. Compare Model Performance
# -----------------------------------------------------------------------------
print("\n--- Comparing Model Performance ---")

# Sort models by test RMSE
results_df_sorted = results_df.sort_values('Test RMSE')
print("\nModel performance comparison (sorted by Test RMSE):")
print(results_df_sorted[['Model', 'Test RMSE', 'Test MAE', 'Test R²']])

# Save results to CSV
results_df_sorted.to_csv('../reports/model_comparison.csv', index=False)

# Plot model comparison
plt.figure(figsize=(12, 6))
sns.barplot(x='Model', y='Test RMSE', data=results_df_sorted)
plt.title('Model Comparison - Test RMSE')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('../reports/model_comparison_rmse.png')
plt.close()

plt.figure(figsize=(12, 6))
sns.barplot(x='Model', y='Test R²', data=results_df_sorted)
plt.title('Model Comparison - Test R²')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('../reports/model_comparison_r2.png')
plt.close()

# -----------------------------------------------------------------------------
# 5. Feature Importance Analysis
# -----------------------------------------------------------------------------
print("\n--- Feature Importance Analysis ---")

# Get feature importance from the best tree-based model (based on Test RMSE)
best_model_name = results_df_sorted.iloc[0]['Model']
if best_model_name in ["Random Forest", "Gradient Boosting", "XGBoost", "LightGBM"]:
    if best_model_name == "Random Forest":
        best_model = rf
    elif best_model_name == "Gradient Boosting":
        best_model = gb
    elif best_model_name == "XGBoost":
        best_model = xgb_model
    else:  # LightGBM
        best_model = lgb_model
    
    # Get feature importance
    if hasattr(best_model, 'feature_importances_'):
        feature_importance = pd.DataFrame({
            'Feature': X.columns,
            'Importance': best_model.feature_importances_
        })
        feature_importance = feature_importance.sort_values('Importance', ascending=False).head(20)
        
        # Plot feature importance
        plt.figure(figsize=(12, 8))
        sns.barplot(x='Importance', y='Feature', data=feature_importance)
        plt.title(f'Top 20 Feature Importance - {best_model_name}')
        plt.tight_layout()
        plt.savefig('../reports/feature_importance.png')
        plt.close()
        
        # Save feature importance to CSV
        feature_importance.to_csv('../reports/feature_importance.csv', index=False)
        
        print("\nTop 10 most important features:")
        print(feature_importance.head(10))
else:
    # For linear models, use coefficients
    if best_model_name in ["Linear Regression", "Ridge Regression", "Lasso Regression", "ElasticNet"]:
        if best_model_name == "Linear Regression":
            best_model = lr
        elif best_model_name == "Ridge Regression":
            best_model = ridge
        elif best_model_name == "Lasso Regression":
            best_model = lasso
        else:  # ElasticNet
            best_model = elastic
        
        # Get coefficients
        coefficients = pd.DataFrame({
            'Feature': X.columns,
            'Coefficient': best_model.coef_
        })
        coefficients['Abs_Coefficient'] = abs(coefficients['Coefficient'])
        coefficients = coefficients.sort_values('Abs_Coefficient', ascending=False).head(20)
        
        # Plot coefficients
        plt.figure(figsize=(12, 8))
        sns.barplot(x='Coefficient', y='Feature', data=coefficients)
        plt.title(f'Top 20 Feature Coefficients - {best_model_name}')
        plt.tight_layout()
        plt.savefig('../reports/feature_coefficients.png')
        plt.close()
        
        # Save coefficients to CSV
        coefficients.to_csv('../reports/feature_coefficients.csv', index=False)
        
        print("\nTop 10 features with highest coefficients:")
        print(coefficients.head(10))

# -----------------------------------------------------------------------------
# 6. Save Best Model for Deployment
# -----------------------------------------------------------------------------
print("\n--- Saving Best Model ---")

# Identify the best model based on Test RMSE
best_model_name = results_df_sorted.iloc[0]['Model']
best_model_rmse = results_df_sorted.iloc[0]['Test RMSE']
best_model_r2 = results_df_sorted.iloc[0]['Test R²']

print(f"Best model: {best_model_name}")
print(f"Test RMSE: {best_model_rmse:.2f}")
print(f"Test R²: {best_model_r2:.4f}")

# Save best model info
best_model_info = {
    'model_name': best_model_name,
    'test_rmse': best_model_rmse,
    'test_r2': best_model_r2
}

with open('../models/best_model_info.pkl', 'wb') as f:
    pickle.dump(best_model_info, f)

print("\nModel building and evaluation completed. Results saved to reports directory.")
