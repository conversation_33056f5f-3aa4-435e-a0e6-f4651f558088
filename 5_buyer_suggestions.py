#!/usr/bin/env python
# coding: utf-8

# # House Price Prediction - Customer Buying Suggestions
# 
# This notebook generates actionable suggestions for home buyers based on the analysis and modeling results.

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.float_format', lambda x: '%.3f' % x)

# Load the original data instead of engineered data to ensure we have all original columns
df = pd.read_csv('../data/data.csv')
print(f"Dataset shape: {df.shape}")

# Create directory for buyer suggestions
os.makedirs('../reports/buyer_suggestions', exist_ok=True)

# -----------------------------------------------------------------------------
# 1. Value Analysis by Neighborhood
# -----------------------------------------------------------------------------
print("\n--- Value Analysis by Neighborhood ---")

# Calculate price per square foot
df['PricePerSqFt'] = df['SalePrice'] / df['GrLivArea']

# Calculate price metrics by neighborhood
neighborhood_metrics = df.groupby('Neighborhood').agg({
    'SalePrice': ['mean', 'median', 'min', 'max', 'count'],
    'PricePerSqFt': ['mean', 'median'],
    'OverallQual': 'mean',
    'GrLivArea': 'mean',
    'YearBuilt': 'mean'
}).reset_index()

# Flatten the multi-index columns
neighborhood_metrics.columns = ['_'.join(col).strip('_') for col in neighborhood_metrics.columns.values]
neighborhood_metrics = neighborhood_metrics.rename(columns={
    'Neighborhood_': 'Neighborhood',
    'SalePrice_mean': 'AvgPrice',
    'SalePrice_median': 'MedianPrice',
    'SalePrice_min': 'MinPrice',
    'SalePrice_max': 'MaxPrice',
    'SalePrice_count': 'Count',
    'PricePerSqFt_mean': 'AvgPricePerSqFt',
    'PricePerSqFt_median': 'MedianPricePerSqFt',
    'OverallQual_mean': 'AvgQuality',
    'GrLivArea_mean': 'AvgSize',
    'YearBuilt_mean': 'AvgYearBuilt'
})

# Calculate value score (higher is better value)
# Value score = (Quality / Price Per Sq Ft) * 100
neighborhood_metrics['ValueScore'] = (neighborhood_metrics['AvgQuality'] / neighborhood_metrics['AvgPricePerSqFt']) * 100
neighborhood_metrics['ValueScore'] = neighborhood_metrics['ValueScore'].round(2)

# Sort by value score (descending)
neighborhood_metrics_by_value = neighborhood_metrics.sort_values('ValueScore', ascending=False)
print("\nNeighborhoods ranked by value score (higher is better value):")
print(neighborhood_metrics_by_value[['Neighborhood', 'ValueScore', 'AvgPrice', 'AvgPricePerSqFt', 'AvgQuality', 'AvgSize', 'AvgYearBuilt', 'Count']].head(10))

# Create bar plot of value score by neighborhood
plt.figure(figsize=(14, 8))
sns.barplot(x='Neighborhood', y='ValueScore', data=neighborhood_metrics_by_value.head(15))
plt.title('Top 15 Neighborhoods by Value Score')
plt.xlabel('Neighborhood')
plt.ylabel('Value Score (Quality/Price per SqFt * 100)')
plt.xticks(rotation=90)
plt.tight_layout()
plt.savefig('../reports/buyer_suggestions/neighborhood_value_score.png')
plt.close()

# Save neighborhood metrics to CSV
neighborhood_metrics_by_value.to_csv('../reports/buyer_suggestions/neighborhood_value_analysis.csv', index=False)

# -----------------------------------------------------------------------------
# 2. Budget-Based Recommendations
# -----------------------------------------------------------------------------
print("\n--- Budget-Based Recommendations ---")

# Define budget ranges
budget_ranges = [
    (0, 150000, 'Low Budget (Under $150K)'),
    (150000, 250000, 'Medium Budget ($150K-$250K)'),
    (250000, 350000, 'High Budget ($250K-$350K)'),
    (350000, float('inf'), 'Luxury Budget (Over $350K)')
]

# Generate recommendations for each budget range
budget_recommendations = []

for min_price, max_price, budget_label in budget_ranges:
    # Filter houses in this budget range
    budget_df = df[(df['SalePrice'] >= min_price) & (df['SalePrice'] < max_price)]
    
    if len(budget_df) == 0:
        continue
    
    # Find top neighborhoods by count
    top_neighborhoods = budget_df['Neighborhood'].value_counts().head(5).index.tolist()
    
    # Calculate average metrics for this budget range
    avg_metrics = {
        'Budget': budget_label,
        'PriceRange': f"${min_price:,.0f} - ${max_price:,.0f}",
        'Count': len(budget_df),
        'AvgSize': budget_df['GrLivArea'].mean(),
        'AvgQuality': budget_df['OverallQual'].mean(),
        'AvgAge': 2010 - budget_df['YearBuilt'].mean(),  # Calculate age from YearBuilt
        'AvgBaths': budget_df['FullBath'].mean() + (0.5 * budget_df['HalfBath'].mean()),  # Estimate total baths
        'AvgBedrooms': budget_df['BedroomAbvGr'].mean(),
        'TopNeighborhoods': ', '.join(top_neighborhoods)
    }
    
    # Find best value neighborhoods in this budget range
    budget_neighborhood_metrics = budget_df.groupby('Neighborhood').agg({
        'SalePrice': ['mean', 'count'],
        'PricePerSqFt': 'mean',
        'OverallQual': 'mean',
        'GrLivArea': 'mean'
    }).reset_index()
    
    # Flatten the multi-index columns
    budget_neighborhood_metrics.columns = ['_'.join(col).strip('_') for col in budget_neighborhood_metrics.columns.values]
    budget_neighborhood_metrics = budget_neighborhood_metrics.rename(columns={
        'Neighborhood_': 'Neighborhood',
        'SalePrice_mean': 'AvgPrice',
        'SalePrice_count': 'Count',
        'PricePerSqFt_mean': 'AvgPricePerSqFt',
        'OverallQual_mean': 'AvgQuality',
        'GrLivArea_mean': 'AvgSize'
    })
    
    # Calculate value score
    budget_neighborhood_metrics['ValueScore'] = (budget_neighborhood_metrics['AvgQuality'] / budget_neighborhood_metrics['AvgPricePerSqFt']) * 100
    
    # Filter neighborhoods with at least 5 houses
    budget_neighborhood_metrics = budget_neighborhood_metrics[budget_neighborhood_metrics['Count'] >= 5]
    
    # Sort by value score
    budget_neighborhood_metrics = budget_neighborhood_metrics.sort_values('ValueScore', ascending=False)
    
    # Get top 3 value neighborhoods
    top_value_neighborhoods = budget_neighborhood_metrics.head(3)['Neighborhood'].tolist()
    avg_metrics['BestValueNeighborhoods'] = ', '.join(top_value_neighborhoods)
    
    budget_recommendations.append(avg_metrics)

# Convert to DataFrame
budget_recommendations_df = pd.DataFrame(budget_recommendations)
print("\nRecommendations by budget range:")
print(budget_recommendations_df)

# Save budget recommendations to CSV
budget_recommendations_df.to_csv('../reports/buyer_suggestions/budget_recommendations.csv', index=False)

# -----------------------------------------------------------------------------
# 3. Feature Prioritization by Budget
# -----------------------------------------------------------------------------
print("\n--- Feature Prioritization by Budget ---")

# Define function to get feature prioritization for a budget range
def get_feature_priorities(min_price, max_price, label):
    # Filter houses in this budget range
    budget_df = df[(df['SalePrice'] >= min_price) & (df['SalePrice'] < max_price)]
    
    if len(budget_df) < 10:  # Need enough data for meaningful analysis
        return None
    
    # Calculate correlation with price within this budget range
    # Use only numerical columns for correlation
    numerical_cols = budget_df.select_dtypes(include=['int64', 'float64']).columns
    correlations = budget_df[numerical_cols].corr()['SalePrice'].sort_values(ascending=False)
    
    # Get top 10 features (excluding price-related features)
    top_features = correlations[~correlations.index.isin(['SalePrice', 'Id'])].head(10)
    
    # Create feature priority recommendations
    priorities = {
        'Budget': label,
        'PriceRange': f"${min_price:,.0f} - ${max_price:,.0f}",
        'TopFeatures': ', '.join(top_features.index.tolist()),
        'FeatureCorrelations': ', '.join([f"{feature}: {corr:.3f}" for feature, corr in top_features.items()])
    }
    
    return priorities

# Generate feature priorities for each budget range
feature_priorities = []
for min_price, max_price, budget_label in budget_ranges:
    priorities = get_feature_priorities(min_price, max_price, budget_label)
    if priorities:
        feature_priorities.append(priorities)

# Convert to DataFrame
feature_priorities_df = pd.DataFrame(feature_priorities)
print("\nFeature priorities by budget range:")
print(feature_priorities_df)

# Save feature priorities to CSV
feature_priorities_df.to_csv('../reports/buyer_suggestions/feature_priorities_by_budget.csv', index=False)

# -----------------------------------------------------------------------------
# 4. Size vs. Quality vs. Location Trade-off Analysis
# -----------------------------------------------------------------------------
print("\n--- Size vs. Quality vs. Location Trade-off Analysis ---")

# Create size categories
df['SizeCategory'] = pd.qcut(df['GrLivArea'], 3, labels=['Small', 'Medium', 'Large'])

# Create quality categories
df['QualityCategory'] = pd.cut(df['OverallQual'], bins=[0, 4, 7, 10], labels=['Basic', 'Good', 'Excellent'])

# Calculate average price for each size-quality combination
size_quality_price = df.groupby(['SizeCategory', 'QualityCategory'])['SalePrice'].agg(['mean', 'count']).reset_index()
size_quality_price.columns = ['Size', 'Quality', 'AvgPrice', 'Count']
size_quality_price['AvgPrice'] = size_quality_price['AvgPrice'].round(2)

print("\nAverage price by size and quality combination:")
print(size_quality_price)

# Create heatmap of average price by size and quality
plt.figure(figsize=(10, 6))
pivot_table = size_quality_price.pivot(index='Size', columns='Quality', values='AvgPrice')
sns.heatmap(pivot_table, annot=True, fmt=',.0f', cmap='YlGnBu')
plt.title('Average House Price by Size and Quality')
plt.tight_layout()
plt.savefig('../reports/buyer_suggestions/size_quality_price_heatmap.png')
plt.close()

# Calculate price premium for quality vs. size
# Base: Small, Basic quality house
base_price = size_quality_price[(size_quality_price['Size'] == 'Small') & (size_quality_price['Quality'] == 'Basic')]['AvgPrice'].values[0]

# Calculate premium for upgrading size vs. upgrading quality
small_excellent_price = size_quality_price[(size_quality_price['Size'] == 'Small') & (size_quality_price['Quality'] == 'Excellent')]['AvgPrice'].values[0]
large_basic_price = size_quality_price[(size_quality_price['Size'] == 'Large') & (size_quality_price['Quality'] == 'Basic')]['AvgPrice'].values[0]

quality_premium = (small_excellent_price - base_price) / base_price * 100
size_premium = (large_basic_price - base_price) / base_price * 100

print(f"\nQuality premium (Small Basic → Small Excellent): {quality_premium:.1f}%")
print(f"Size premium (Small Basic → Large Basic): {size_premium:.1f}%")

# Calculate location premium
# Get average price for each neighborhood
neighborhood_avg_price = df.groupby('Neighborhood')['SalePrice'].mean().sort_values(ascending=False)
top_neighborhood_price = neighborhood_avg_price.iloc[0]
median_neighborhood_price = neighborhood_avg_price.iloc[len(neighborhood_avg_price) // 2]
bottom_neighborhood_price = neighborhood_avg_price.iloc[-1]

top_vs_median_premium = (top_neighborhood_price - median_neighborhood_price) / median_neighborhood_price * 100
median_vs_bottom_premium = (median_neighborhood_price - bottom_neighborhood_price) / bottom_neighborhood_price * 100

print(f"Location premium (Median → Top neighborhood): {top_vs_median_premium:.1f}%")
print(f"Location premium (Bottom → Median neighborhood): {median_vs_bottom_premium:.1f}%")

# Save trade-off analysis to CSV
tradeoff_data = {
    'TradeoffType': ['Quality Premium', 'Size Premium', 'Top Location Premium', 'Median Location Premium'],
    'Description': [
        'Small Basic → Small Excellent',
        'Small Basic → Large Basic',
        'Median → Top neighborhood',
        'Bottom → Median neighborhood'
    ],
    'PremiumPercent': [
        quality_premium,
        size_premium,
        top_vs_median_premium,
        median_vs_bottom_premium
    ]
}
tradeoff_df = pd.DataFrame(tradeoff_data)
tradeoff_df.to_csv('../reports/buyer_suggestions/tradeoff_analysis.csv', index=False)

# -----------------------------------------------------------------------------
# 5. Best Time to Buy Analysis
# -----------------------------------------------------------------------------
print("\n--- Best Time to Buy Analysis ---")

# Analyze price by month sold
monthly_price = df.groupby('MoSold')['SalePrice'].agg(['mean', 'median', 'count']).reset_index()
monthly_price.columns = ['Month', 'AvgPrice', 'MedianPrice', 'Count']
monthly_price['AvgPrice'] = monthly_price['AvgPrice'].round(2)
monthly_price['MedianPrice'] = monthly_price['MedianPrice'].round(2)

# Sort by month
monthly_price = monthly_price.sort_values('Month')

# Add month names
month_names = {
    1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
    7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
}
monthly_price['MonthName'] = monthly_price['Month'].map(month_names)

print("\nAverage price by month sold:")
print(monthly_price)

# Create bar plot of average price by month
plt.figure(figsize=(12, 6))
sns.barplot(x='MonthName', y='AvgPrice', data=monthly_price)
plt.title('Average House Price by Month Sold')
plt.xlabel('Month')
plt.ylabel('Average Price ($)')
plt.tight_layout()
plt.savefig('../reports/buyer_suggestions/price_by_month.png')
plt.close()

# Calculate price difference from average
overall_avg_price = df['SalePrice'].mean()
monthly_price['PriceDiffFromAvg'] = monthly_price['AvgPrice'] - overall_avg_price
monthly_price['PriceDiffPercent'] = (monthly_price['PriceDiffFromAvg'] / overall_avg_price) * 100
monthly_price['PriceDiffPercent'] = monthly_price['PriceDiffPercent'].round(2)

# Identify best months to buy (lowest prices)
best_months = monthly_price.sort_values('AvgPrice').head(3)
print("\nBest months to buy (lowest average prices):")
print(best_months[['MonthName', 'AvgPrice', 'PriceDiffPercent', 'Count']])

# Save monthly price analysis to CSV
monthly_price.to_csv('../reports/buyer_suggestions/price_by_month.csv', index=False)

# -----------------------------------------------------------------------------
# 6. Generate Buyer Personas and Recommendations
# -----------------------------------------------------------------------------
print("\n--- Buyer Personas and Recommendations ---")

# Define buyer personas
buyer_personas = [
    {
        'name': 'First-time Homebuyer',
        'budget': 150000,
        'priorities': 'Affordability, Good schools, Low maintenance',
        'description': 'Young couple or individual looking for their first home with limited budget.'
    },
    {
        'name': 'Growing Family',
        'budget': 250000,
        'priorities': 'Space, Good schools, Safe neighborhood, Multiple bedrooms',
        'description': 'Family with children or planning for children, needing more space.'
    },
    {
        'name': 'Luxury Buyer',
        'budget': 400000,
        'priorities': 'High quality, Premium location, Modern amenities, Status',
        'description': 'Established professional seeking premium home with high-end features.'
    },
    {
        'name': 'Downsizer',
        'budget': 200000,
        'priorities': 'Quality, Low maintenance, Convenient location, Single level',
        'description': 'Empty nesters or retirees looking to downsize from larger family home.'
    },
    {
        'name': 'Investor',
        'budget': 180000,
        'priorities': 'Value, Rental potential, Appreciation, Low maintenance',
        'description': 'Looking for properties with good rental income potential or appreciation.'
    }
]

# Generate recommendations for each persona
for persona in buyer_personas:
    # Filter houses within 20% of budget
    min_price = persona['budget'] * 0.8
    max_price = pers
(Content truncated due to size limit. Use line ranges to read in chunks)